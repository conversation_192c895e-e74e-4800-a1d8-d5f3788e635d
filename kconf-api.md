 **Kconf OpenAPI使用指南V2**

# Contact us

> 问题咨询或反馈可联系应用号：Kconf客服，或者加入用户群

<table style="border:none;border-collapse:collapse"><colgroup><col width="485"><col width="485"></colgroup><tbody><tr style="height:0px"><td style="border-left:solid #e1e3e6 1px;border-right:solid #e1e3e6 1px;border-bottom:solid #e1e3e6 1px;border-top:solid #e1e3e6 1px;vertical-align:middle;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;text-align: center;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',<PERSON><PERSON>,sans-serif,Sim<PERSON><PERSON>,'宋体',serif;color: #000000;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;"><img src="https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:7719465057039334381fcADP9WKcrJNq5om9YVxTjg08:1750324565032" width="432px;" height="104px;" style="border: none; transform: rotate(0.00rad); -webkit-transform: rotate(0.00rad);"></span></p></td><td style="border-left:solid #e1e3e6 1px;border-right:solid #e1e3e6 1px;border-bottom:solid #e1e3e6 1px;border-top:solid #e1e3e6 1px;vertical-align:middle;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;text-align: center;"></p></td></tr></tbody></table>

# 前置说明

 **kconf OpenAPI用于三方平台管理kconf配置数据（**  **一般用于业务管理后台中**  **，对业务配置数据的可视化展示和操作，**  **不允许私自变更** [快手稳定性红线规范](https://docs.corp.kuaishou.com/k/home/<USER>/fcAAOAjFvzhLVfABtwOWHw_NY#section=h.ex6g6m7iff1d) **），不建议使用openAPI作为配置加载/渲染的路径**

1.  **kconf不适合并发写入的场景**  **(**  **目录范围内**  **同一时刻只有一次变更可以成功)，不要把kconf当做数据库，kconf的定位是服务配置，适合的是非频繁更改的配置型数据，高**  **频更新的数据更像是数据，考虑用mysql，redis等来存储和更改；**
> 因为底层kess存储的一致性检查的原因，对同一个二级目录下的配置不要同时发起更新请求，否则其中某些请求会失败；
2.  **如果只是读取配置不要使用OpenAPI方式（http）**  **，请使用对应语言的sdk（订阅/推送配置）；**
> 前端使用请在业务接口进行下发使用，如果没有对应语言的sdk，或者使用场景没有kess，再考虑使用token；
3.  **应杜绝从测试环境修改线上配置**  **的情况，避免引发线上故障**  **；**
> 实际由于这种情况引发过线上故障，请不要让历史重现；
4.  **kconf配置通过KESS通道下发，为保障KESS集群稳定性，**  **单个二级目录(全部配置数据累加)上限1.8M，无其他限制；**
> 具体存储空间占用情况，可在二级目录下的配置大小tab进行查看；


## 常见问题

1\. 测试环境，见[开发测试](https://docs.corp.kuaishou.com/k/home/<USER>/fcADP9WKcrJNq5om9YVxTjg08#section=h.6smk55aqgp1i)

2\. kconf环境定义及字段，见[配置创建/更新接口入参](https://docs.corp.kuaishou.com/k/home/<USER>/fcADP9WKcrJNq5om9YVxTjg08#section=h.kwx7a4d82k08)

3\. PRT环境，需要根据服务/分组/kws等（如PRT）信息确定生效范围的需要使用自定义环境（[接口说明](https://docs.corp.kuaishou.com/k/home/<USER>/fcADP9WKcrJNq5om9YVxTjg08#section=h.kwx7a4d82k08)），概念说明见[配置环境](https://docs.corp.kuaishou.com/d/home/<USER>



# 一、使用流程

1. 申请token
2. 开发测试

## 1.1 申请token

### 申请

> [https://kconf.corp.kuaishou.com/#/\_auth/tokenApply](https://kconf.corp.kuaishou.com/#/_auth/tokenApply)

token申请的过程类似注册新用户，由系统管理员进行审批，审批通过后，有最大十分钟的生效延迟；

 **id** ：用于标识访问端系统的id，小驼峰格式，类似新用户的英文名，也是java kuaishou-kconf-server-api中构建客户端时的appName参数；

 **名字** ：id的中文对应名称，类似新用户的中文名，最终会体现在配置修改历史中的修改人名字；

 **用途** ：申请token的用途，按照实际进行填写；

 **账户等级** ：在az逃生时，将根据等级进行可用性保障，请根据token的重要性进行选择；

 **写配额** ：使用该token进行配置写入操作时的限流值，默认1QPM，每分钟1次； **（请不要使用token高频次变更）**

 **读配额** ：使用该token进行配置读取操作时的限流值，默认10QPM，每分钟10次；

 **流量说明** ：说明将会使用本token进行怎样的读写操作，例如：定时任务中使用，每分钟读写一次；

 **权限** ：token拥有的可变更配置的权限，使用token进行读取配置不限制权限，可填写一级目录、二级目录、完整的key，可填写多个；

根据实际的业务场景选择合适的权限类型；

 **查看权限** ：只能读取配置；

 **测试权限** ：配置读取以及修改staging和candidate环境配置；

 **更新权限** ：配置读取以及更新已经存在的配置，不能创建；

 **编辑权限** ：配置读写以及创建权限；

 **管理权限** ：配置读写、创建以及目录创建的权限；

> 如果需要对已经通过审批的token进行调整，请在token详情页中使用编辑，由系统管理员审核

 **管理员** ：token的管理员，默认当前申请人为管理员，管理员用户可查看及修改token；

![](https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:-5339208219357588970fcADP9WKcrJNq5om9YVxTjg08:1750324565033)



### 修改

如果想对之前申请的token进行修改，可在[我有权限的token](https://kconf.corp.kuaishou.com/#/_auth/myToken)中，找到曾经申请的或者有权限token，进入详情页，可使用编辑按钮对其进行修改，提交之后，待系统管理员审批后生效。

![](https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:4541256181326773360fcADP9WKcrJNq5om9YVxTjg08:1750324565033)

![](https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:2210612778612120687fcADP9WKcrJNq5om9YVxTjg08:1750324565033)![](https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:-1146973403009573300fcADP9WKcrJNq5om9YVxTjg08:1750324565033)

### 如何使用

使用api访问快手配置中心的接口时，需提供token用以鉴权。token的使用方式为：在HTTP请求的header中添加：Authorization: Token xxxxx（xxxxx为申请到的token）。



#### 开发测试

 **环境说明**

 **开发测试阶段修改线上配置非常危险** ，推荐 **使用kconf测试环境调试openApi** （域名[https://kconf.staging.kuaishou.com/](https://kconf.staging.kuaishou.com/)）

由于 **数据与上线隔离** ，测试环境无需使用线上环境申请的token（开发测试阶段时可使用token: bbmyPkfG7Ci12JwILLqYqIv9xD98XApq，此token只可用于修改[test目录](https://kconf.staging.kuaishou.com/#/test)下的配置，可在test目录下随意测试）

测试环境 **仅用于调试httpClient和接口，修改不会触发配置下发** 。服务在staging/candidate环境中通过kconf sdk订阅到的配置，统一由线上环境维护



 **请求demo**

配置读取

```Bash
curl -XGET -H "Authorization: Token bbmyPkfG7Ci12JwILLqYqIv9xD98XApq" "https://kconf.staging.kuaishou.com/api/config/get?key=test.kconf.demo"
```

配置更新

```Bash
curl -XPOST "https://kconf.staging.kuaishou.com/api/config/subconfig/update" -H "Content-Type: application/x-www-form-urlencoded" -H "Authorization: Token bbmyPkfG7Ci12JwILLqYqIv9xD98XApq" -d "key=test.kconf.demo&comment=更新测试&stage=production&snapshotId=-1&content={\"a\":1}"
```



# 二、接口说明



## 域名

测试环境请求域名：[https://kconf.staging.kuaishou.com](https://kconf.staging.kuaishou.com) （无法在线上idc调用）（仅用于开发调用api阶段的测试，配置并不会实际下发，无法使用sdk读取）

> 如果需要在线上调用测试环境，可使用这个域名：[https://kconf-test.corp.kuaishou.com](https://kconf-test.corp.kuaishou.com)


线上环境请求域名：[https://kconf.corp.kuaishou.com](https://kconf.corp.kuaishou.com)



## 常用接口详解

### 配置读取

#### 接口

#### 说明

1. 除特殊目录外，不限制配置读取；
2. 加密配置需要将token设置为秘钥的管理员（例如token的id为「xxx」，则需要将「app-xxx」设置为秘钥的管理员）；

#### 入参

<table style="border:none;border-collapse:collapse;width:971px"><colgroup><col width="0.2*"><col width="0.2*"><col width="0.2*"><col width="0.2*"><col width="0.2*"></colgroup><tbody><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">参数</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">类型</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是否必须</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">含义</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">示例</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">key</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">配置完整的key</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">test.kconf.demo</span></p></td></tr></tbody></table>

#### 返回值

```JSON
{
  "data": {
    "key": "test.kconf.demo", // 配置key
    "description": "示例配置", // 配置描述
    "type": "json", // 配置类型
    "version": 4, // 配置版本
    "status": "normal", // 配置状态，normal为正常,offline为下线
    "createdDate": "2023-01-18 19:22:46", // 创建时间
    "creator": "闫昊鹏", // 创建人
    "modifier": "闫昊鹏", // 最近修改人
    "modifiedDate": "2023-01-18 19:48:24", // 最近修改时间
    "subConfigs": [ // 各个环境的配置
      {
        "snapshotId": 121876,
        "stage": "production",
        "data": "{\"a\":1}",
        "modifier": "测试程序",
        "modifiedDate": "2023-01-18 19:23:37"
      },
      {
        "snapshotId": 121886, // 环境配置id
        "stage": "gray_3", // 代表环境
        "data": "{\n    \"env\":\"custom\"\n}", // 配置内容
        "modifier": "闫昊鹏", // 最近修改人
        "modifiedDate": "2023-01-18 19:48:24",  //  最近修改日期
        "grayRelease": {  // 自定义环境参数
          "name": "自定义环境", // 自定义环境名称
          "hosts": [ // 主机列表
            "host1"
          ],
          "services": [], // 服务
          "kwsServices": [ // kws环境参数
            {
              "ksn": "service1", // ksn 服务名
              "stage": "PREONLINE", // 环境
              "az": "YZ", // az
              "physicalAz": "HB1AZ2", // 物理az
              "lane": "lane1", // 泳道
              "group": "pre-gray1" // 服务分组
            }
          ],
          "podNames": [ // 实例名
            "pod1"
          ]
        }
      },
      {
        "snapshotId": 121879,
        "stage": "staging",
        "data": "{\n  \"env\": \"staging\"\n}",
        "modifier": "闫昊鹏",
        "modifiedDate": "2023-01-18 19:28:59"
      }
    ],
    "favorite": false, // 是否收藏
    "oldConfigs": [], // 关联zk，已废弃
    "needReview": false, // 是否强制review
    "resetable": true, // 是否可修改
    "resetables": [ // 可修改属性
      "key",
      "type"
    ],
    "settings": { // 配置设置
      "inherited": {},
      "computed": {}
    },
    "subConfigRelationMap": {}, // 分级发布映射配置
    "showGrayServices": false // 是否展示自定义环境第二tab
  },
  "message": "success",
  "result": 1
}
```




### 创建新配置key

#### 接口

#### 说明

1. 先要创建配置key，才能对这个key进行环境配置更新；

#### 入参

<table style="border:none;border-collapse:collapse;width:971px"><colgroup><col width="0.2*"><col width="0.2*"><col width="0.2*"><col width="0.2*"><col width="0.2*"></colgroup><tbody><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">参数</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">类型</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是否必须</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">含义</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">示例</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">key</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">配置完整的key</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">test.kconf.demo2</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">desc</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">配置描述</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:13px;font-family:'Microsoft Yahei';color: #1f2329;background-color: #ffffff;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">我是描述</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">type</span></p><br></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">配置类型</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">string</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">encryptKey</span></p><br></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">否</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">加密key</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">kconf.demo</span></p></td></tr></tbody></table>

#### 返回值

```JSON
{
  "data": {
    "key": "test.kconf.demo2",
    "description": "我是描述",
    "type": "string",
    "version": 0,
    "status": "normal",
    "createdDate": "2023-02-01 20:16:46",
    "creator": "闫昊鹏",
    "modifier": "闫昊鹏",
    "modifiedDate": "2023-02-01 20:16:46",
    "subConfigs": [],
    "needReview": false
  },
  "message": "success",
  "result": 1
}
```



### 环境配置创建/更新

#### 接口

#### 说明

1. 至少需要配置的测试权限；
2. 根据参数stage，如果不存在则创建，如果存在则更新；
3. gray开头的入参为自定义环境的环境参数，仅当创建/更新自定义环境时需要传参，custom开头的入参为分级发布的自定义环境参数，程序变更时一般不需要传入；
4. 注意是要发送Content-Type为application/x-www-form-urlencoded类型的POST请求
5. 因为底层kess存储的一致性检查的原因，对同一个二级目录下的配置不要同时发起更新请求，否则其中某些请求会失败。kconf适合的是非频繁更改的配置型数据，高频更新的数据更像是数据，考虑用mysql，redis等来存储和更改。
6. 因为kconf的更新是全量内容的替换，需要注意一致性的问题。比如配置内容是list型数据，更新时先调用[配置读取](https://docs.corp.kuaishou.com/k/home/<USER>/fcADP9WKcrJNq5om9YVxTjg08#section=h.sxryj1rkzs99)接口得到当前的list列表，更改后再提交新的list列表内容，如果并发更新就会有一致性的问题。使用snapshotId参数可以减少此种情况，并不能避免。给get+update这个过程整体加锁，可以避免，如果是多实例服务，需要分布式锁。
7.  **本接口更新参数比较复杂，可以在页面上实际更新一下配置，抓取对应请求，看如何传参的，这个接口就是kconf配置详情页面上更新配置的接口；**
   ![](https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:-2446615861300217746fcADP9WKcrJNq5om9YVxTjg08:1750324565035)

#### 入参




<table style="border:none;border-collapse:collapse"><colgroup><col width="167"><col width="74"><col width="60"><col width="340"><col width="310"></colgroup><tbody><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">参数</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">类型</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">必填</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">含义</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;background-color: #d1f2ff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">示例</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">key</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">配置完整的key</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">test.kconf.demo</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">content</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">配置内容</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">{"a":1}</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">comment</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">修改描述</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">配置更新</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">snapshotId</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">long</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">环境配置id，这个值需要调用/api/config/get接口，从中得到相应环境的当前的snapshotId值并在更改内容时做为参数传递以用于做版本一致性检查，如果对配置的更新内容一致性要求不高则可以传-1以忽略一致性检查</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">3870567409</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">stage（</span><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">kconf环境id，非天琴stage</span><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">）</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">是</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><ol style="margin-bottom:0px;margin-top:0px;"><li dir="ltr" style="list-style-type:decimal;font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">kconf各常规环境的标识，见右侧表格；</span></p></li><li dir="ltr" style="list-style-type:decimal;font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">需要根据服务/分组/kws等（如PRT）信息确定生效范围的，需要使用</span><a href="https://docs.corp.kuaishou.com/d/home/<USER>" style="text-decoration:none;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #2375d1;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">自定义环境</span></a><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">；</span></p></li><li dir="ltr" style="list-style-type:decimal;font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">自定义环境</span><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">新建时为</span><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">gray</span><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">，修改时为</span><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">gray_xxx</span><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">（使用getConfig()方法中拿到的stage，如）</span></p></li></ol><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #000000;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;"><img src="https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:7861449086248064093fcADP9WKcrJNq5om9YVxTjg08:1750324565035" width="321px;" height="161.98153846153846px;" style="border: none; transform: rotate(0.00rad); -webkit-transform: rotate(0.00rad);"></span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><br><div dir="ltr" style="margin-left:0pt;"><table style="border:none;border-collapse:collapse"><colgroup><col width="179"><col width="206"></colgroup><tbody><tr style="height:0px"><td style="border-left:solid #e2fad4 1px;border-right:solid #e2fad4 1px;border-bottom:solid #e2fad4 1px;border-top:solid #e2fad4 1px;vertical-align:top;background-color: #37acff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">环境</span></p></td><td style="border-left:solid #e2fad4 1px;border-right:solid #e2fad4 1px;border-bottom:solid #e2fad4 1px;border-top:solid #e2fad4 1px;vertical-align:top;background-color: #37acff;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">值</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #e2fad4 1px;border-top:solid #e2fad4 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">默认环境</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #e2fad4 1px;border-top:solid #e2fad4 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">production</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #e2fad4 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">国内标准生产环境(HB1+HB2)</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #e2fad4 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">unit_cn</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">北京生产环境(HB1)</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">hb1</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">北京生产环境HB1AZ1</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">physical_az1</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">乌兰生产环境(HB2)</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">hb2</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">乌兰生产环境HB2AZ1</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">hb2az1</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">staging环境</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">staging</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">新加坡生产环境(SGP)</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">singapore_production</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">新加坡生产环境SGPAZ1</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">sgp_paz1</span></p></td></tr></tbody></table></div><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">其他环境查看</span><a href="https://kconf.corp.kuaishou.com/api/islogin" style="text-decoration:none;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #2375d1;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:underline;vertical-align:baseline;white-space:pre-wrap;">接口</span></a><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">中availableStages字段</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">grayName</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #fc3232;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">自定义环境，必填</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">自定义环境的名字</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">我是名字</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">grayHosts</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td rowspan="3" style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #fc3232;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">自定义环境，至少填写一个</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">自定义环境的主机,英文逗号分隔</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">host,host3</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">grayPodNames</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">自定义环境的实例，英文逗号分隔</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">pod,pod3</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">grayKws</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #e9e9e9 1px;border-top:solid #e9e9e9 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #fc3232;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">生效范围对应的kws参数</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><a href="https://docs.corp.kuaishou.com/d/home/<USER>" style="text-decoration:none;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #2375d1;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">Kconf 自定义环境详解</span></a></p><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">[{"ksn":"kconf-api","lane":"PRT","stage":"PROD_REGRESSION_TEST","group":"pre-gray1","az":"YZ","physicalAz":"HB1AZ2"},{"ksn":"kconf-api2","lane":"PRT","stage":"PROD_REGRESSION_TEST","group":"pre-gray1","az":"YZ","physicalAz":"HB1AZ2"}]</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:line-through;vertical-align:baseline;white-space:pre-wrap;">grayServices</span></p><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #fc3232;background-color: #00000000;font-weight:bold;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">不建议使用</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:line-through;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.45;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:line-through;vertical-align:baseline;white-space:pre-wrap;">否</span></p></td><td style="border-left:solid #e1e3e6 1px;border-right:solid #e1e3e6 1px;border-bottom:solid #e9e9e9 1px;border-top:solid #e1e3e6 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:line-through;vertical-align:baseline;white-space:pre-wrap;">自定义环境第二个tab的服务，英文逗号分隔</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:line-through;vertical-align:baseline;white-space:pre-wrap;">service1,service2</span></p></td></tr><tr style="height:0px"><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">modifier</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">String</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">否</span></p></td><td style="border-left:solid #e1e3e6 1px;border-right:solid #e1e3e6 1px;border-bottom:solid #e9e9e9 1px;border-top:solid #e1e3e6 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">实际的变更人，用于后续的记录和变更分析，有的话建议传</span></p></td><td style="border-left:solid #d8d8d8 1px;border-right:solid #d8d8d8 1px;border-bottom:solid #d8d8d8 1px;border-top:solid #d8d8d8 1px;vertical-align:top;padding:7px 7px 7px 7px"><p dir="ltr" toggle="0" blockstyle="" style="line-height:1.35;margin-top:0pt;margin-bottom:0pt;"><span style="font-size:15px;font-family:PingFang SC, Arial,'Microsoft YaHei','微软雅黑','黑体',Heiti,sans-serif,SimSun,'宋体',serif;color: #1f2329;background-color: #00000000;font-weight:normal;font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre-wrap;">zhangsan</span></p></td></tr></tbody></table>

#### 返回值

```JSON
{
  "data": {
    "snapshotId": 123263, // 环境配置id
    "stage": "gray_3", // 修改的哪个环境
    "data": "{\"a\":1}", // 配置内容
    "modifier": "闫昊鹏", // 更新人
    "modifiedDate": "2023-02-01 17:32:55", // 更新时间
    "grayRelease": { // 自定义环境参数
      "name": "自定义环境",  // 自定义环境名字
      "hosts": [ // 主机
        "host1"
      ],
      "services": null,
      "kwsServices": [ // kws环境变量
        {
          "ksn": "service1", // 服务名
          "stage": "PREONLINE", // 环境
          "az": "YZ", // az
          "physicalAz": "HB1AZ2", // 物理az
          "lane": "lane1", // 泳道
          "group": "pre-gray1" // 用户分组
        }
      ],
      "podNames": [ // 实例名字
        "pod1"
      ]
    }
  },
  "message": "success",
  "result": 1
}
```



# 三、开发测试-java

## 依赖引入

```XML
<dependency>
    <groupId>kuaishou</groupId>
    <artifactId>kuaishou-kconf-server-api</artifactId>
</dependency>
```

## code demo

注意：

-  **测试环境** （[https://kconf.staging.kuaishou.com/#/](https://kconf.staging.kuaishou.com/#/)）数据与线上隔离（配置、token等）， **只用来验证接口调用** ，提交内容无法在任何环境读取；
-  **测试环境** ，调试时，请使用KconfServerApi.newDebugApi()， **只能在staging环境使用** ，且只有test目录的读写权限

### ```Java
String key = "test.kconf.demo";
String productionStage = "production";

// 1.KconfServerApi对象构建
// 测试域名，请求发送到 https://kconf.staging.kuaishou.com/#/ 只用来测试管理域，配置无法实际读取，且只有test目录下的权限；
KconfServerApi debugApi = KconfServerApi.newDebugApi();
// 线上域名，请求发送到 https://kconf.corp.kuaishou.com/ ，第一个参数为申请token时的id，第二个参数为得到的token；
KconfServerApi api = KconfServerApi.newKconfApi("appId", "appToken");

// 上面两种构建测试域名和线上域名KconfServerApi的方式等价于下面的方式
// 需要kuaishou-kconf-server-api大于等于1.0.134
// 定义超时时间，可使用kconf有动态调整超时时间的能力
private static final Kconf<Duration> timeout =
            Kconfs.ofLong("kconfBusiness.temp.longTest", 10).mapper(Duration::ofSeconds).build();
// 测试域名构建
KconfServerApi debugApi = KconfServerApi.newBuilder()
                .useTestServerApi()  // 指定使用kconf.test域名，使用公共测试token，有test目录权限，不指定则为线上域名
                .setTimeout(timeout) // timeout可写死，例如：() -> Duration.ofSeconds(5)，不传默认为5s
                .build();
// 线上域名构建
KconfServerApi api = KconfServerApi.newBuilder()
                .setAppName("appId")
                .setToken("appToken")
                .setTimeout(timeout)
                .build();

// 2.配置读取,各项属性含义参考接口的配置读取返回值
GetConfigResponse config = debugApi.getConfig(key);

// 3.配置创建
debugApi.createKconfKey(key, ConfigValueType.STRING, "我是描述");

// 4.常规环境（非自定义环境）环境配置创建/更新
debugApi.updateConfig(key, productionStage, -1, "{\"aa\":\"aa\"}", "我是修改描述", "修改人");

/** 5.cas更新，更新失败时进行重试，不支持自定义环境
  *
  * @param key kconf key
  * @param stage stage
  * @param mergeFunc oldConfig -> newConfig，根据旧配置生成新配置
  * @param comment 修改描述
  * @param modifier 修改人, 写邮箱前缀
  * @param maxRetry 最大重试次数
  * @param retryWaitMs 重试间隔, 单位ms
  * @return 是否更新成功
  * @see #KCONF_CAS_FAILED = 1  CAS更新失败，在自旋maxRetry次后，仍失败
  * @see #KCONF_CAS_SUCC = 2  CAS更新成功
  * @see #KCONF_CAS_ALREADY_EQUAL = 3 待更新的值和原始值一致
  */
int casInt = debugApi.casUpdateConfig(key, productionStage, old -> old, "修改描述", "修改人", 3, 100);

// 5.更新/创建prt自定义环境，返回数据是所有环境的配置，想要找到指定的自定义环境，可以使用自定义环境名称或者环境参数进行匹配；自定义环境的stage参数比较特殊，不是固定的值，当创建时，值为gray，创建后会生成stage值，形如 gray_xxx，xxx是数字，生成后不会改变，要知道值是多少，就需要先读取配置，找到对应的自定义环境；
String prtName = "prt";
KconfSubConfig kconfSubConfig = ofNullable(config.getData())
          .map(KconfConfig::getSubConfigs)
          .orElse(Collections.emptyList())
          .stream()																			//使用灰度名称进行匹配，也可使用环境参数进行匹配
          .filter(sub -> sub.getGrayRelease() != null && grayName.equals(sub.getGrayRelease().getName()))
          .findFirst()
          .orElse(null);
if (kconfSubConfig != null) {
  		debugApi.updateConfig(key, kconfSubConfig.getStage(), kconfSubConfig.getSnapshotId(), "{\"prt\":\"prt\"}",
                        "修改描述", kconfSubConfig.getGrayRelease(), "修改人");
} else {
      GrayRelease grayRelease = new GrayRelease();
      grayRelease.setName(grayName);
      KwsService kwsService = new KwsService();
      kwsService.setStage("PROD_REGRESSION_TEST");
      grayRelease.setKwsServices(Lists.newArrayList(kwsService));
      debugApi.updateConfig(key, "gray", 0, "{\"prt\":\"prt\"}", "修改描述", grayRelease, "修改人");
}

// 更多方法支持，查看源码；

```



# 四、开发测试-其他语言

更多语言的支持请查看：[https://halo.corp.kuaishou.com/help/product/18/learn-path](https://halo.corp.kuaishou.com/help/product/18/learn-path)



# 五、问题排查

### 监控

> [https://grafana.corp.kuaishou.com/d/gz7jDC1Gz/kconf-openapi-jian-kong-da-pan?orgId=3&var-group=subtag&var-result=All&var-stage=All&var-uri=All&var-host=All&var-token=All](https://grafana.corp.kuaishou.com/d/gz7jDC1Gz/kconf-openapi-jian-kong-da-pan?orgId=3&var-group=subtag&var-result=All&var-stage=All&var-uri=All&var-host=All&var-token=All)

token选择申请token的id，前面会增加字符串'"app-"，例如申请的token的id为kconfIdea，则选择 "app-kconfIdea"，主要查看请求失败的监控，error.302，代表错误码为302，对照错误码查看含义，进行相应的处理；

![](https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:-5339829798023582625fcADP9WKcrJNq5om9YVxTjg08:1750324565036)

![](https://docs.corp.kuaishou.com/image/api/external/load/out?code=fcADP9WKcrJNq5om9YVxTjg08:-814907672392292406fcADP9WKcrJNq5om9YVxTjg08:1750324565036)

### 错误码

> [kconf errorCode错误码](https://docs.corp.kuaishou.com/d/home/<USER>
