.DS_Store
.cache
npm-debug.log
Thumbs.db
node_modules/
.build/
.vscode/extensions/**/out/
extensions/**/dist/
/out*/
/extensions/**/out/
build/node_modules
coverage/
test_data/
test-results/
test-results.xml
vscode.lsif
vscode.db
/.profile-oss
/cli/target
/cli/openssl
product.overrides.json
*.snap.actual
.vscode-test
**/local-agent/**
src/vs/workbench/contrib/void/**


extensions/kwaipilot/build/**
extensions/kwaipilot/out/**
extensions/kwaipilot/setting-ui/**
extensions/kwaipilot/bridge/**
extensions/kwaipilot/resources/**
extensions/kwaipilot/assets/**

dist/**


# 忽略 kwaipilot 子模块的更改
src/vs/kwaipilot/
