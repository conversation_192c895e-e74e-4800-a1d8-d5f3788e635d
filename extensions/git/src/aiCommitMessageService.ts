/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { LogOutputChannel, workspace, window, Uri, extensions, WorkspaceConfiguration } from 'vscode';
import TelemetryReporter from '@vscode/extension-telemetry';
import { Repository } from './repository';
import * as crypto from 'crypto';
import * as os from 'os';
import * as path from 'path';
import * as fs from 'fs';

export interface DiffInfo {
	stagedFiles: FileChange[];
	diffContent: string;
	repositoryInfo: RepositoryInfo;
}
export interface FileChange {
	path: string;
	status: 'added' | 'modified' | 'deleted' | 'renamed';
	oldPath?: string;
}

export interface RepositoryInfo {
	name: string;
	branch: string;
	root: string;
}

export interface IAICommitMessageService {
	generateCommitMessage(repository: Repository): Promise<string | undefined>;
}

export class AICommitMessageService implements IAICommitMessageService {
	constructor(
		private readonly logger: LogOutputChannel,
		private readonly telemetryReporter: TelemetryReporter,
	) { }

	async generateCommitMessage(repository: Repository): Promise<string | undefined> {
		try {
			this.logger.info('[AI Commit] Starting AI commit message generation...');

			// 1. Check if AI feature is enabled
			const config = workspace.getConfiguration('git', Uri.file(repository.root));
			const isEnabled = config.get<boolean>('enableAICommitMessage', true);
			this.logger.info(`[AI Commit] AI feature enabled: ${isEnabled}`);

			if (!isEnabled) {
				return undefined;
			}

			// 2. Collect git diff information
			let diffInfo = await this.collectDiffInfo(repository, true); // Try staged changes first
			if (!diffInfo || diffInfo.stagedFiles.length === 0) {
				this.logger.info('[AI Commit] No staged changes found, trying working tree changes');
				diffInfo = await this.collectDiffInfo(repository, false); // Try working tree changes
				if (!diffInfo || diffInfo.stagedFiles.length === 0) {
					this.logger.info('[AI Commit] No changes found in working tree either');
					return undefined;
				}
			}
			this.logger.info(`[AI Commit] Found ${diffInfo.stagedFiles.length} files with changes`);
			// 3. Call AI service to generate commit message
			const generatedMessage = await this.callAIService(diffInfo, config);
			if (!generatedMessage) {
				this.logger.warn('[AI Commit] AI service returned empty message');
				return undefined;
			}

			// 4. Post-process and validate
			const processedMessage = this.postProcessMessage(generatedMessage, config);

			this.logger.info(`[AI Commit] Generated message: ${processedMessage}`);
			return processedMessage;

		} catch (error) {
			this.logger.error(`[AI Commit] Failed to generate commit message: ${error}`);
			return undefined;
		}
	}

	private async collectDiffInfo(repository: Repository, useStaged: boolean = true): Promise<DiffInfo | undefined> {
		try {
			// Get changes (either staged or working tree)
			const changes = useStaged
				? repository.indexGroup.resourceStates
				: repository.workingTreeGroup.resourceStates;

			if (changes.length === 0) {
				return undefined;
			}

			// Build file change information
			const stagedFiles: FileChange[] = changes.map(change => ({
				path: change.resourceUri.fsPath.replace(repository.root + '/', ''),
				status: this.mapStatusToChangeType(change.type),
				oldPath: change.renameResourceUri?.fsPath.replace(repository.root + '/', '')
			}));

			// Get diff content
			const diffContent = await this.getDiff(repository, useStaged);

			// Build repository information
			const repositoryInfo: RepositoryInfo = {
				name: repository.root.split('/').pop() || 'unknown',
				branch: repository.headShortName || 'unknown',
				root: repository.root
			};

			return {
				stagedFiles,
				diffContent,
				repositoryInfo
			};

		} catch (error) {
			this.logger.error(`[AI Commit] Failed to collect diff info: ${error}`);
			return undefined;
		}
	}
	private async getDiff(repository: Repository, useStaged: boolean = true): Promise<string> {
		try {
			// git diff --cached for staged changes, git diff for working tree changes
			const diff = await repository.diff(useStaged);
			return diff;
		} catch (error) {
			this.logger.error(`[AI Commit] Failed to get diff: ${error}`);
			return '';
		}
	}

	private mapStatusToChangeType(status: number): 'added' | 'modified' | 'deleted' | 'renamed' {
		// This needs to map based on VSCode Git extension status codes
		// Simplified version of the mapping
		switch (status) {
			case 0: return 'added';
			case 1: return 'modified';
			case 2: return 'deleted';
			case 3: return 'renamed';
			default: return 'modified';
		}
	}

	private async callAIService(diffInfo: DiffInfo, config: WorkspaceConfiguration): Promise<string> {
		try {
			this.logger.info('[AI Commit] Calling AI service for commit message generation');
			// Get kwaipilot.settings.proxy from user settings, default to https://kwaipilot.corp.kuaishou.com if not set
			const kwaipilotConfig = workspace.getConfiguration('kwaipilot');
			const proxyUrl = kwaipilotConfig.get<string>('settings.proxy');
			// Remove trailing slashes
			const baseUrl = (proxyUrl || 'https://kwaipilot.corp.kuaishou.com').replace(/\/+$/, '');
			const completionPath = '/eapi/kwaipilot/chat/completion';
			// Get max length configuration
			const maxLength = config.get('aiCommitMessageMaxLength', 72);
			const outputRule = `
1. Keep the output concise and avoid unnecessary line breaks and spaces.
2. The maximum number of characters is ${maxLength}.
3. Please submit the information in Simplified Chinese, for example: 'feat: 添加 ai 解析器'
			`;

			// Create prompt to be sent
			const prompt = this.buildPrompt(diffInfo);

			const body = {
				sessionId: crypto.randomUUID(), // Generate unique session ID
				chatId: crypto.randomUUID(), // Generate unique chat ID
				question: prompt,
				chatRecordList: [],
				sourceTab: 'intelligentChat',
				useSearch: false,
				refFiles: [],
				chatModelParam: {
					modelType: 'kwaipilot_pro_32k'
				},
				linkRepoId: '',
				refLinks: [],
				requestType: 'COMMON_CHAT',
				rules: [outputRule, this.getKwaipilotRule('gcm.mdr')].filter(Boolean),
				username: process.env.USER || process.env.USERNAME || '', // Get system username
				deviceId: workspace.getConfiguration('telemetry').get<string>('machineId') || crypto.randomUUID(), // Use VSCode's machine ID
				platform: 'kwaipilot-vscode',
				pluginVersion: extensions.getExtension('kuaishou.kwaipilot')?.packageJSON?.version || '', // Get plugin version
				deviceName: os.hostname(), // Get device name
				deviceModel: os.platform(), // Get operating system platform
				deviceOsVersion: os.release(), // Get operating system version
				projectName: workspace.name || path.basename(workspace.workspaceFolders?.[0]?.uri.fsPath || ''), // Get workspace name
				// Get git remote URL
				gitRemote: diffInfo.repositoryInfo.name,
				// Get current branch name
				branchName: diffInfo.repositoryInfo.branch,
				openedFilePath: window.activeTextEditor?.document.uri.fsPath || '' // Get currently opened file path
			};

			this.logger.info('[AI Commit] AI service request body', body);
			// Construct full URL
			const url = `${baseUrl}${completionPath}`;
			// Record telemetry data
			this.telemetryReporter.sendTelemetryEvent('aiCommit.generate', {
				repoName: diffInfo.repositoryInfo.name,
				filesCount: String(diffInfo.stagedFiles.length)
			});
			// Send request
			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Accept': 'text/event-stream'
				},
				body: JSON.stringify(body)
			});

			if (!response.ok) {
				throw new Error(`API request failed with status ${response.status}`);
			}
			// Process text/event-stream response, convert it to JSON
			if (response.headers.get('content-type')?.includes('text/event-stream')) {
				const result = await this.processEventStream(response);
				this.logger.info('[AI Commit] Received event stream response', result);
				return result;
			} else {
				// If not stream response, directly parse JSON
				const data = await response.json();
				this.logger.info('[AI Commit] AI service response received', data);
				// @ts-ignore
				return data?.result?.message || 'No commit message suggestion available';
			}

		} catch (error) {
			this.logger.error(`[AI Commit] Error calling AI service: ${error instanceof Error ? error.message : String(error)}`);
			throw error;
		}
	}

	/**
	 * Process text/event-stream response, convert it to complete message
	 */
	private async processEventStream(response: Response): Promise<string> {
		if (!response.body) {
			throw new Error('Response body is null');
		}

		const reader = response.body.getReader();
		const decoder = new TextDecoder('utf-8');
		let fullMessage = '';

		try {
			// Used to accumulate complete message
			let accumulatedMessage = '';

			while (true) {
				const { done, value } = await reader.read();
				if (done) {
					break;
				}

				// Decode binary data to text
				const chunk = decoder.decode(value, { stream: true });
				this.logger.info(`[AI Commit] Received chunk: ${chunk}`);

				// Process SSE formatted data
				const lines = chunk.split('\n');
				for (const line of lines) {
					if (line.startsWith('data:')) {
						// data:{"type":"reply","reply":"添加 "}
						try {
							// Extract JSON string after data:
							const jsonStr = line.substring(5).trim();
							if (jsonStr) {
								const eventData = JSON.parse(jsonStr);
								if (eventData.type === 'reply' && eventData.reply) {
									accumulatedMessage += eventData.reply;
								}
							}
						} catch (e) {
							this.logger.error(`[AI Commit] Error parsing SSE data: ${e}`);
						}
					} else {
						//  {"type":"reply","reply":"REA"}
						try {
							const jsonStr = line.trim();
							if (jsonStr) {
								const eventData = JSON.parse(jsonStr);
								if (eventData.type === 'reply' && eventData.reply) {
									accumulatedMessage += eventData.reply;
								}
							}
						} catch (e) {
							this.logger.error(`[AI Commit] Error parsing SSE data: ${e}`);
						}
					}
				}
			}

			// Return accumulated complete message
			fullMessage = accumulatedMessage;
			this.logger.info(`[AI Commit] Full message from stream: ${fullMessage}`);

			// Create JSON formatted response
			const jsonResponse = {
				result: {
					message: fullMessage
				}
			};

			// Record converted JSON response
			this.logger.info('[AI Commit] Converted to JSON response', jsonResponse);

			return jsonResponse.result.message;
		} catch (error) {
			this.logger.error(`[AI Commit] Error processing event stream: ${error}`);
			throw error;
		}
	}

	/**
	 * Helper method to build AI prompt from diff information.
	 * This method is provided for user convenience when implementing AI service calls.
	 */
	private buildPrompt(diffInfo: DiffInfo): string {
		const { stagedFiles, diffContent, repositoryInfo } = diffInfo;

		// 1. 构建基本提示信息
		let prompt = `Generate a concise commit message for the following changes in repository "${repositoryInfo.name}" on branch "${repositoryInfo.branch}":\n\n`;

		// 2. 添加文件变更信息
		prompt += 'Changed files:\n';
		const filteredFiles = this.filterRelevantFiles(stagedFiles);
		filteredFiles.forEach(file => {
			prompt += `- ${file.status}: ${file.path}\n`;
		});

		// 如果过滤后没有文件，使用原始文件列表
		if (filteredFiles.length === 0 && stagedFiles.length > 0) {
			stagedFiles.forEach(file => {
				prompt += `- ${file.status}: ${file.path}\n`;
			});
		}

		// 3. 处理 diff 内容
		const MAX_DIFF_LENGTH = 30_000; // 约 30KB，为其他内容留出空间

		// 如果 diff 内容较小，直接使用
		if (diffContent.length <= MAX_DIFF_LENGTH) {
			prompt += '\nDiff content:\n```diff\n' + diffContent + '\n```';
		} else {
			// 处理大型 diff
			const processedDiff = this.truncateDiff(diffContent, filteredFiles, MAX_DIFF_LENGTH);
			prompt += '\nDiff content (truncated):\n```diff\n' + processedDiff + '\n```';
		}

		prompt += '\nPlease generate a commit message following conventional commit format (type: description).';
		return prompt;
	}

	private getKwaipilotRule(filename: string) {
		// Get rule content for specified filename
		// Workspace root directory
		// .kwaipilot/rules/{filename}
		const rulePath = path.join(workspace.workspaceFolders?.[0]?.uri.fsPath || '', '.kwaipilot', 'rules', filename);
		// Check if exists
		if (!fs.existsSync(rulePath)) {
			return;
		}
		const ruleContent = fs.readFileSync(rulePath, 'utf8');
		return ruleContent;
	}

	private postProcessMessage(message: string, config: WorkspaceConfiguration): string {
		if (!message) {
			return '';
		}

		// Get max length configuration
		const maxLength = config.get('aiCommitMessageMaxLength', 72);

		// Clean up and format message
		let processedMessage = message.trim();

		// Remove extra quotes
		if (processedMessage.startsWith('"') && processedMessage.endsWith('"')) {
			processedMessage = processedMessage.slice(1, -1);
		}

		// Limit length
		if (processedMessage.length > maxLength) {
			processedMessage = processedMessage.substring(0, maxLength - 3) + '...';
		}
		// Ensure first letter is lowercase (conventional commit format)
		if (processedMessage.length > 0) {
			const colonIndex = processedMessage.indexOf(':');
			if (colonIndex > 0 && colonIndex < processedMessage.length - 1) {
				const type = processedMessage.substring(0, colonIndex);
				const description = processedMessage.substring(colonIndex + 1).trim();
				if (description.length > 0) {
					processedMessage = type + ': ' + description.charAt(0).toLowerCase() + description.slice(1);
				}
			}
		}

		return processedMessage;
	}

	/**
	 * 过滤出相关文件，忽略不重要的文件
	 */
	private filterRelevantFiles(files: FileChange[]): FileChange[] {
		// 忽略的文件模式
		const ignorePatterns = [
			/\.gitignore$/,
			/\.DS_Store$/,
			/package-lock\.json$/,
			/yarn\.lock$/,
			/pnpm-lock\.yaml$/,
			/\.lock$/,
			/\.png$/,
			/\.jpe?g$/,
			/\.gif$/,
			/\.ico$/,
			/\.svg$/,
			/\.woff2?$/,
			/\.ttf$/,
			/\.eot$/,
			/\.mp[34]$/,
			/\.wav$/,
			/\.ogg$/,
			/\.webm$/,
			/\.pdf$/,
			/\.zip$/,
			/\.gz$/,
			/\.tar$/,
			/\.min\.(js|css)$/,
			/node_modules\//,
			/dist\//,
			/build\//,
			/\.vscode\//
		];

		return files.filter(file => {
			// 检查文件是否应该被忽略
			return !ignorePatterns.some(pattern => pattern.test(file.path));
		});
	}

	/**
	 * 智能截断 diff 内容
	 */
	private truncateDiff(diffContent: string, relevantFiles: FileChange[], maxLength: number): string {
		// 按文件分割 diff
		const fileDiffs = diffContent.split('diff --git');

		if (fileDiffs.length <= 1) {
			// 如果只有一个文件，简单截断
			return this.simpleChunkTruncate(diffContent, maxLength);
		}

		// 构建文件路径到 diff 内容的映射
		const filePathToDiff = new Map<string, string>();

		// 处理每个文件的 diff
		for (let i = 1; i < fileDiffs.length; i++) {
			const fileDiff = fileDiffs[i];
			const filePathMatch = fileDiff.match(/a\/([^\s]+)/);

			if (filePathMatch) {
				const filePath = filePathMatch[1];
				filePathToDiff.set(filePath, 'diff --git' + fileDiff);
			}
		}

		// 优先保留重要文件的 diff
		let result = '';
		const importantExtensions = ['.ts', '.js', '.tsx', '.jsx', '.vue', '.py', '.java', '.go', '.rs', '.c', '.cpp', '.h', '.cs'];

		// 1. 首先添加重要文件类型
		for (const file of relevantFiles) {
			const ext = path.extname(file.path);
			if (importantExtensions.includes(ext) && filePathToDiff.has(file.path)) {
				const fileDiff = filePathToDiff.get(file.path)!;
				if (result.length + fileDiff.length <= maxLength) {
					result += fileDiff;
					filePathToDiff.delete(file.path);
				} else {
					// 如果添加整个文件会超出限制，添加截断版本
					const remainingLength = maxLength - result.length;
					if (remainingLength > 500) { // 确保至少有足够空间添加有意义的内容
						result += this.truncateFileDiff(fileDiff, remainingLength);
					}
					break;
				}
			}
		}

		// 2. 然后添加其他文件
		for (const [_, fileDiff] of filePathToDiff.entries()) {
			if (result.length + fileDiff.length <= maxLength) {
				result += fileDiff;
			} else {
				// 如果剩余空间不足，添加摘要信息
				const remainingFiles = filePathToDiff.size;
				result += `\n\n... and ${remainingFiles} more files with changes (diff truncated due to size) ...\n`;
				break;
			}
		}

		return result || this.simpleChunkTruncate(diffContent, maxLength);
	}

	/**
	 * 截断单个文件的 diff
	 */
	private truncateFileDiff(fileDiff: string, maxLength: number): string {
		// 提取文件头部信息
		const headerEndIndex = fileDiff.indexOf('@@');
		if (headerEndIndex === -1) {
			return fileDiff.substring(0, maxLength);
		}

		// 保留头部
		const header = fileDiff.substring(0, headerEndIndex + fileDiff.substring(headerEndIndex).indexOf('\n') + 1);

		// 计算剩余可用长度
		const remainingLength = maxLength - header.length - 40; // 40 是截断提示的长度

		// 提取变更行（以 + 或 - 开头的行）
		const contentPart = fileDiff.substring(headerEndIndex);
		const lines = contentPart.split('\n');
		let extractedContent = '';
		let changeLines = 0;

		for (const line of lines) {
			if (line.startsWith('+') || line.startsWith('-')) {
				if (extractedContent.length + line.length + 1 <= remainingLength) {
					extractedContent += line + '\n';
					changeLines++;
				}
			}
		}

		return header + extractedContent + `\n... (${lines.length - changeLines} more lines) ...\n`;
	}

	/**
	 * 简单的分块截断策略
	 */
	private simpleChunkTruncate(content: string, maxLength: number): string {
		if (content.length <= maxLength) {
			return content;
		}

		// 保留前 40%，中间省略，后 40%
		const chunkSize = Math.floor(maxLength * 0.4);
		const firstChunk = content.substring(0, chunkSize);
		const lastChunk = content.substring(content.length - chunkSize);

		return firstChunk +
			'\n\n... [diff content truncated, showing first and last parts only] ...\n\n' +
			lastChunk;
	}
}

