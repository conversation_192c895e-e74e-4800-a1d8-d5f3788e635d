#!/bin/bash

set -e

# 默认启用签名和公证
ENABLE_CODESIGN="true"
ENABLE_NOTARIZE="true"

echo "当前系统文件句柄限制：$(ulimit -n)"

# 设置下载URL
DOWNLOAD_URL="https://h3.static.yximgs.com/kos/nlav11354/ai-ide/Kwaipilot-darwin-arm64-04d112840e3-20250623_160653.zip"
# DOWNLOAD_URL="https://h1.static.yximgs.com/kos/nlav11354/ai-ide/VSCode-darwin-arm64.d8b2a8067883a46e.zip"


# 创建临时目录（在当前目录下）
TEMP_DIR="./temp_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TEMP_DIR"
FILENAME=$(basename "$DOWNLOAD_URL")
BASE_FILENAME="${FILENAME%.*}"
SIGNED_FILENAME="${BASE_FILENAME}.signed.zip"

echo "开始处理: $FILENAME"
echo "临时目录: $TEMP_DIR"

# 下载文件
echo "正在下载 $DOWNLOAD_URL..."
curl -L "$DOWNLOAD_URL" -o "$TEMP_DIR/$FILENAME"

# 解压文件
echo "正在解压文件..."
ditto -xk "$TEMP_DIR/$FILENAME" "$TEMP_DIR/extracted"

# 查找应用程序
APP_PATH=$(find "$TEMP_DIR/extracted" -name "*.app" -type d | head -n 1)
if [ -z "$APP_PATH" ]; then
  echo "错误: 未找到.app文件"
  rm -rf "$TEMP_DIR"
  exit 1
fi

echo "找到应用程序: $APP_PATH"

# 创建临时 settings 文件
TEMP_SETTINGS="$TEMP_DIR/temp_settings.py"
cat settings.py | sed "s|{{APP_PATH}}|$APP_PATH|g" > "$TEMP_SETTINGS"

# 使用临时 settings 文件创建 DMG
dmgbuild -s "$TEMP_SETTINGS" Kwaipilot Kwaipilot-dmgbuild.dmg

# 清理临时文件
rm -f "$TEMP_SETTINGS"
