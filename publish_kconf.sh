#!/bin/bash

# 用法: publish_kconf.sh <json路径> <平台key> [quality] [kconf_token]

set -e

# 日志函数
log() {
  echo -e "[publish_kconf] $1"
}

if [ $# -lt 2 ]; then
  log "用法: $0 <json路径> <平台key> [quality] [kconf_token]"
  exit 1
fi

MAIN_JSON_PATH="$1"
PLATFORM_KEY="$2"
QUALITY_VALUE="${3:-dev}"
KCONF_TOKEN="${4:-$KCONF_TOKEN}"

log "MAIN_JSON_PATH=$MAIN_JSON_PATH"
log "PLATFORM_KEY=$PLATFORM_KEY"
log "QUALITY_VALUE=$QUALITY_VALUE"
log "KCONF_TOKEN=${KCONF_TOKEN:0:8}..."

# 1. 选择 kconf key
if [ "$QUALITY_VALUE" == "production" ]; then
    KCONF_KEY='dmo.aidevops.kwaipilot-ide-product-prod'
else
    KCONF_KEY='dmo.aidevops.kwaipilot-ide-product'
fi
log "KCONF_KEY=$KCONF_KEY"

# 2. 检查 json 文件
if [ ! -f "$MAIN_JSON_PATH" ]; then
    log "❌ 未找到主配置 json 文件: $MAIN_JSON_PATH"
    exit 1
fi

# 3. 读取新内容
NEW_JSON_CONTENT=$(cat "$MAIN_JSON_PATH" | python3 -c 'import sys,json; print(json.dumps(json.load(sys.stdin), ensure_ascii=False))')
log "已读取新 JSON 内容，长度: ${#NEW_JSON_CONTENT}"

# 4. 获取 commit message 作为 comment
KCONF_COMMENT=$(git log -1 --pretty=format:"%s" 2>/dev/null || echo "auto update")
log "KCONF_COMMENT=$KCONF_COMMENT"

# 5. 获取 snapshotId
KCONF_STAGE="production"
KCONF_API_URL="https://kconf.corp.kuaishou.com/api/config"
if [ -z "$KCONF_TOKEN" ]; then
    log "❌ 缺少 KCONF_TOKEN 环境变量"
    exit 1
fi

log "请求 KCONF snapshotId..."
KCONF_GET_RESP=$(curl -s -XGET -H "Authorization: Token $KCONF_TOKEN" "$KCONF_API_URL/get?key=$KCONF_KEY")

# 增强的响应校验
if [ -z "$KCONF_GET_RESP" ]; then
    log "❌ KCONF_GET_RESP 为空，可能网络异常或权限不足。"
    exit 1
fi

# 输出响应内容用于调试
log "KCONF_GET_RESP 前100个字符: ${KCONF_GET_RESP:0:100}"

# 更精确的 JSON 格式检查
if ! echo "$KCONF_GET_RESP" | python3 -c "import sys, json; json.load(sys.stdin)" 2>/dev/null; then
    log "❌ KCONF_GET_RESP 非有效 JSON 内容，完整内容如下："
    echo "$KCONF_GET_RESP"
    exit 1
fi

# 用 python3 解析 snapshotId，增加错误处理
# 先将响应保存到临时文件，避免 heredoc 问题
TEMP_RESP_FILE="/tmp/kconf_resp_$$.json"
echo "$KCONF_GET_RESP" > "$TEMP_RESP_FILE"

SNAPSHOT_ID=$(python3 -c "
import sys, json
try:
    with open('$TEMP_RESP_FILE', 'r', encoding='utf-8') as f:
        data = json.load(f)

    sid = -1
    # 检查响应结构
    if 'data' not in data:
        print('响应中缺少 data 字段', file=sys.stderr)
        print(-1)
        sys.exit(0)

    subconfigs = data.get('data', {}).get('subConfigs', [])
    if not subconfigs:
        print('subConfigs 为空', file=sys.stderr)
        print(-1)
        sys.exit(0)

    for sub in subconfigs:
        if sub.get('stage') == '$KCONF_STAGE':
            sid = sub.get('snapshotId', -1)
            break

    print(sid)
except json.JSONDecodeError as e:
    print(f'JSON 解析错误: {e}', file=sys.stderr)
    print(-1)
except Exception as e:
    print(f'解析 snapshotId 时出错: {e}', file=sys.stderr)
    print(-1)
")

log "SNAPSHOT_ID=$SNAPSHOT_ID"
if [ -z "$SNAPSHOT_ID" ]; then
    SNAPSHOT_ID="-1"
fi

# 6. 拉取当前 kconf 配置内容，增加错误处理
log "解析旧 kconf 配置内容..."
KCONF_OLD_JSON=$(python3 -c "
import sys, json
try:
    with open('$TEMP_RESP_FILE', 'r', encoding='utf-8') as f:
        data = json.load(f)

    subconfigs = data.get('data', {}).get('subConfigs', [])
    content = '{}'

    print(f'找到 {len(subconfigs)} 个子配置', file=sys.stderr)

    for i, sub in enumerate(subconfigs):
        stage = sub.get('stage', 'unknown')
        print(f'子配置 {i}: stage={stage}', file=sys.stderr)

        if sub.get('stage') == '$KCONF_STAGE':
            try:
                raw_content = sub.get('data', '{}')
                print(f'原始配置长度: {len(raw_content)}', file=sys.stderr)
                print(f'原始配置前200字符: {raw_content[:200]}', file=sys.stderr)

                # 如果 data 字段是字符串，需要再次解析
                if isinstance(raw_content, str):
                    # 验证是否为有效 JSON 字符串
                    json.loads(raw_content)
                    content = raw_content
                else:
                    # 如果是对象，直接序列化
                    content = json.dumps(raw_content, ensure_ascii=False)

            except json.JSONDecodeError as e:
                print(f'解析配置数据时 JSON 错误: {e}', file=sys.stderr)
                content = '{}'
            except Exception as e:
                print(f'解析配置数据时出错: {e}', file=sys.stderr)
                content = '{}'
            break

    print(content)
except json.JSONDecodeError as e:
    print(f'JSON 解析错误: {e}', file=sys.stderr)
    print('{}')
except Exception as e:
    print(f'解析配置内容时出错: {e}', file=sys.stderr)
    print('{}')
")

log "KCONF_OLD_JSON长度: ${#KCONF_OLD_JSON}"

# 验证获取到的旧配置是否为有效 JSON
if ! echo "$KCONF_OLD_JSON" | python3 -c "import sys, json; json.load(sys.stdin)" 2>/dev/null; then
    log "⚠️ 获取到的旧配置不是有效 JSON，使用空对象"
    KCONF_OLD_JSON='{}'
fi

# 7. 合并新平台内容到旧配置，增加错误处理
log "合并新平台内容到旧配置..."
MERGED_JSON=$(python3 -c "
import sys, json
try:
    old = json.loads(sys.argv[1])
    new = json.loads(sys.argv[2])
    key = sys.argv[3]
    old[key] = new
    # 生成格式化的 JSON（带缩进和换行）
    print(json.dumps(old, ensure_ascii=False, indent=2))
except Exception as e:
    print(f'合并 JSON 时出错: {e}', file=sys.stderr)
    sys.exit(1)
" "$KCONF_OLD_JSON" "$NEW_JSON_CONTENT" "$PLATFORM_KEY")

if [ $? -ne 0 ]; then
    log "❌ JSON 合并失败"
    exit 1
fi

log "MERGED_JSON长度: ${#MERGED_JSON}"

# 8. 上传合并后的 json 到 kconf
log "推送聚合配置到 kconf..."
KCONF_UPDATE_RESP=$(curl -s -XPOST "$KCONF_API_URL/subconfig/update" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -H "Authorization: Token $KCONF_TOKEN" \
    -d "key=$KCONF_KEY" \
    -d "content=$MERGED_JSON" \
    -d "comment=$KCONF_COMMENT" \
    -d "stage=$KCONF_STAGE" \
    -d "snapshotId=$SNAPSHOT_ID")

log "KCONF_UPDATE_RESP: $KCONF_UPDATE_RESP"

# 增强更新结果检查
KCONF_RESULT=$(echo "$KCONF_UPDATE_RESP" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('result', 0))
except Exception as e:
    print(f'解析更新响应时出错: {e}', file=sys.stderr)
    print(0)
" 2>/dev/null || echo "0")

if [ "$KCONF_RESULT" == "1" ]; then
    log "✅ kconf 配置推送成功: $KCONF_KEY (平台: $PLATFORM_KEY)"
else
    log "❌ kconf 配置推送失败: $KCONF_KEY"
    log "响应内容: $KCONF_UPDATE_RESP"
    # 清理临时文件
    rm -f "$TEMP_RESP_FILE"
    exit 1
fi

# 清理临时文件
rm -f "$TEMP_RESP_FILE"
