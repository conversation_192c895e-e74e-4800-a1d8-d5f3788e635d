#!/bin/bash

set -e

echo "当前系统文件句柄限制：$(ulimit -n)"

# KCDN上传函数
function kcdn_upload() {
    local FILE_PATH="$1"

    # 检查文件是否存在
    if [ ! -f "$FILE_PATH" ]; then
        echo "错误: 文件不存在: $FILE_PATH"
        return 1
    fi

    # 检查上传token环境变量
    if [ -z "$UPLOAD_TOKEN" ]; then
        echo "错误: 缺少上传token环境变量 UPLOAD_TOKEN"
        return 1
    fi

    local FILENAME=$(basename "$FILE_PATH")
    local PROJECT_ID="team-auto-cli"
    local UPLOAD_DIR="ai-ide"
    local KCDN_URL="https://kcdn.corp.kuaishou.com/api/kcdn/v1/service/npmUpload/single"

    echo "开始上传文件到KCDN: $FILENAME"
    echo "  项目ID: $PROJECT_ID"
    echo "  目录: /$UPLOAD_DIR/"
    echo "  文件路径: $FILE_PATH"

    # 使用curl上传文件
    local UPLOAD_RESPONSE=$(curl -s --location --request POST \
        "${KCDN_URL}?token=${UPLOAD_TOKEN}" \
        --form "file=@${FILE_PATH}" \
        --form "pid=${PROJECT_ID}" \
        --form "filename=${UPLOAD_DIR}/${FILENAME}" \
        --form "allowRewrite=true" \
        --form "allowMD5=false" \
        --form "allowHash=false" \
        --form "requestInfo.uploaderType=2" \
        --form "requestInfo.serviceName=ai-editor-build" \
        --form "requestInfo.requestUri=/upload/dmg")

    # 检查上传结果
    local UPLOAD_CODE=$(echo "$UPLOAD_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('code', -1))" 2>/dev/null || echo "-1")

    if [ "$UPLOAD_CODE" == "0" ]; then
        local CDN_URL=$(echo "$UPLOAD_RESPONSE" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('cdnUrl', ''))" 2>/dev/null || echo "")
        echo "✅ 文件上传成功: $FILENAME"
        echo "   CDN URL: $CDN_URL"

        # 如果是JSON文件，输出更新配置信息
        if [[ "$FILENAME" == *.json ]]; then
            echo "   📝 更新配置文件已上传，可通过以下URL访问:"
            echo "   $CDN_URL"
        fi

        return 0
    else
        echo "❌ 文件上传失败: $FILENAME"
        echo "   错误代码: $UPLOAD_CODE"
        echo "   响应内容: $UPLOAD_RESPONSE"
        return 1
    fi
}

# 使用BUILDTYPE变量
if [ -z "$BUILDTYPE" ]; then
  # 如果没有指定BUILDTYPE，则根据当前平台和架构自动设置
  PLATFORM=$(uname | tr '[:upper:]' '[:lower:]')
  ARCH=$(uname -m)

  # 映射架构
  if [ "$ARCH" == "x86_64" ]; then
    ARCH="x64"
  elif [ "$ARCH" == "arm64" ] || [ "$ARCH" == "aarch64" ]; then
    ARCH="arm64"
  elif [ "$ARCH" == "armv7l" ]; then
    ARCH="armhf"
  fi

  # 映射平台
  if [ "$PLATFORM" == "darwin" ]; then
    PLATFORM="darwin"
  elif [ "$PLATFORM" == "linux" ]; then
    PLATFORM="linux"
  elif [[ "$PLATFORM" == *"mingw"* ]] || [[ "$PLATFORM" == *"msys"* ]] || [[ "$PLATFORM" == *"cygwin"* ]]; then
    PLATFORM="win32"
  fi

  # 自动设置BUILDTYPE
  BUILDTYPE="vscode-$PLATFORM-$ARCH"
  echo "未指定BUILDTYPE，自动设置为: $BUILDTYPE"
else
  echo "使用指定的BUILDTYPE: $BUILDTYPE"

  # 从BUILDTYPE解析出PLATFORM和ARCH
  if [[ "$BUILDTYPE" =~ vscode-([^-]+)-([^-]+) ]]; then
    export PLATFORM=${BASH_REMATCH[1]}
    export ARCH=${BASH_REMATCH[2]}
    echo "解析出平台: $PLATFORM, 架构: $ARCH"
  else
    echo "警告: BUILDTYPE格式不正确，可能无法被build-skip-typecheck.sh正确识别"
  fi
fi

# 确保PLATFORM和ARCH被导出，以便被子脚本使用
export PLATFORM
export ARCH


# 获取传入的参数
APP_PATH="$1"
ENABLE_CODESIGN="${2:-"true"}"
ENABLE_NOTARIZE="${3:-"true"}"
CODESIGN_IDENTITY="$4"
APPLE_ID="$5"
APPLE_APP_SPECIFIC_PASSWORD="$6"
APPLE_TEAM_ID="$7"
KCONF_TOKEN="$8"

# 如果没有提供应用路径，则显示使用方法并退出
if [ -z "$APP_PATH" ]; then
  echo "使用方法: $0 <应用路径> [启用签名] [启用公证] [签名身份] [Apple ID] [App特定密码] [团队ID]"
  echo "例如: $0 /path/to/app.app true true \"Developer ID Application: Your Name\" \"<EMAIL>\" \"app-specific-password\" \"TEAM123456\""
  exit 1
fi

# 检查应用路径是否存在
if [ ! -d "$APP_PATH" ]; then
  echo "错误: 应用路径不存在: $APP_PATH"
  exit 1
fi

echo "开始处理应用: $APP_PATH"

# 创建临时目录（在当前目录下）
TEMP_DIR="./temp_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TEMP_DIR"

# 代码签名
if [ "$ENABLE_CODESIGN" == "true" ]; then
  echo "开始代码签名..."

  # 如果未指定签名身份，则自动从钥匙串获取
  if [ -z "$CODESIGN_IDENTITY" ]; then
    echo "未指定签名身份，尝试从钥匙串获取可用证书..."

    # 首先尝试获取 Developer ID Application 证书（推荐用于分发）
    DEVELOPER_ID_CERT=$(security find-identity -v -p codesigning | grep "Developer ID Application" | head -n 1 | grep -o -E '([0-9A-F]{40})')

    # 如果没有 Developer ID，则尝试获取 Mac Developer 证书
    if [ -z "$DEVELOPER_ID_CERT" ]; then
      MAC_DEVELOPER_CERT=$(security find-identity -v -p codesigning | grep "Mac Developer" | head -n 1 | grep -o -E '([0-9A-F]{40})')

      # 如果没有 Mac Developer，则尝试获取 Apple Development 证书
      if [ -z "$MAC_DEVELOPER_CERT" ]; then
        APPLE_DEVELOPMENT_CERT=$(security find-identity -v -p codesigning | grep "Apple Development" | head -n 1 | grep -o -E '([0-9A-F]{40})')

        # 如果没有任何开发证书，则尝试获取任何可用的签名证书
        if [ -z "$APPLE_DEVELOPMENT_CERT" ]; then
          ANY_CERT=$(security find-identity -v -p codesigning | head -n 1 | grep -o -E '([0-9A-F]{40})')

          if [ -z "$ANY_CERT" ]; then
            echo "错误: 在钥匙串中找不到任何可用的签名证书。"
            echo "请设置 CODESIGN_IDENTITY 环境变量，或者在钥匙串中导入有效的签名证书。"
            rm -rf "$TEMP_DIR"
            exit 1
          else
            CODESIGN_IDENTITY="$ANY_CERT"
            echo "使用找到的签名证书: $CODESIGN_IDENTITY"
          fi
        else
          CODESIGN_IDENTITY="$APPLE_DEVELOPMENT_CERT"
          echo "使用 Apple Development 证书: $CODESIGN_IDENTITY"
        fi
      else
        CODESIGN_IDENTITY="$MAC_DEVELOPER_CERT"
        echo "使用 Mac Developer 证书: $CODESIGN_IDENTITY"
      fi
    else
      CODESIGN_IDENTITY="$DEVELOPER_ID_CERT"
      echo "使用 Developer ID Application 证书: $CODESIGN_IDENTITY"
    fi

    # 获取证书的完整名称，用于显示
    CERT_NAME=$(security find-identity -v -p codesigning | grep "$CODESIGN_IDENTITY" | sed -E 's/.*"([^"]+)".*/\1/')
    if [ ! -z "$CERT_NAME" ]; then
      echo "证书名称: $CERT_NAME"
    fi
  fi

  # 创建临时授权文件
  ENTITLEMENTS_FILE="$TEMP_DIR/entitlements.plist"
  cat > "$ENTITLEMENTS_FILE" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.network.server</key>
    <true/>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
</dict>
</plist>
EOF

  # 清理扩展属性，这可能会导致签名问题
  echo "清理扩展属性..."
  xattr -cr "$APP_PATH"

  # 签名所有内部组件
  echo "签名动态库和辅助应用..."

  # 签名所有可执行文件和二进制文件
  find "$APP_PATH" -type f \( -name "*.dylib" -o -name "*.so" -o -name "*.node" -o -name "rg" -o -name "spawn-helper" -o -name "chrome_crashpad_handler" -o -name "ShipIt" -o -name "kwaipilot-binary" \) | while read -r BINARY; do
    echo "签名二进制文件: $BINARY"
    # 添加 --options runtime 启用强化运行时，添加 --timestamp 启用安全时间戳
    codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" --entitlements "$ENTITLEMENTS_FILE" "$BINARY"
  done

  # 签名框架
  find "$APP_PATH/Contents/Frameworks" -name "*.framework" -type d | while read -r FRAMEWORK; do
    echo "签名框架: $FRAMEWORK"

    # 特别处理Electron Framework
    if [[ "$FRAMEWORK" == *"Electron Framework.framework"* ]]; then
      echo "检测到Electron Framework，进行特殊处理..."

      # 先签名所有内部可执行文件
      find "$FRAMEWORK" -type f \( -perm +111 -o -name "*.dylib" -o -name "*.so" -o -name "*.node" \) | while read -r BINARY; do
        echo "签名Electron Framework内部组件: $BINARY"
        codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" --entitlements "$ENTITLEMENTS_FILE" "$BINARY"
      done

      # 然后签名整个框架
      codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" --entitlements "$ENTITLEMENTS_FILE" "$FRAMEWORK"
    else
      # 常规框架签名流程
      find "$FRAMEWORK" -type f \( -name "*.dylib" -o -name "*.so" -o -name "*.node" \) | while read -r BINARY; do
        codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" --entitlements "$ENTITLEMENTS_FILE" "$BINARY"
      done
      codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" --entitlements "$ENTITLEMENTS_FILE" "$FRAMEWORK"
    fi
  done

  # 签名辅助应用
  find "$APP_PATH/Contents/Frameworks" -name "*.app" -type d | while read -r HELPER_APP; do
    echo "签名辅助应用: $HELPER_APP"
    codesign --force --options runtime --timestamp --entitlements "$ENTITLEMENTS_FILE" --sign "$CODESIGN_IDENTITY" "$HELPER_APP"
  done

  # 修复MacOS目录中的可执行文件
  MACOS_DIR="$APP_PATH/Contents/MacOS"
  if [ -d "$MACOS_DIR" ]; then
    echo "签名MacOS目录中的可执行文件..."
    find "$MACOS_DIR" -type f -perm +111 | while read -r EXEC; do
      echo "签名可执行文件: $EXEC"
      codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" --entitlements "$ENTITLEMENTS_FILE" "$EXEC"
    done
  fi

  # 签名Resources目录中的内容
  RESOURCES_DIR="$APP_PATH/Contents/Resources"
  if [ -d "$RESOURCES_DIR" ]; then
    echo "签名Resources目录中的可执行文件..."
    find "$RESOURCES_DIR" -type f -perm +111 | while read -r EXEC; do
      echo "签名可执行文件: $EXEC"
      codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" --entitlements "$ENTITLEMENTS_FILE" "$EXEC"
    done
  fi

  # 签名主应用
  echo "签名主应用..."
  codesign --force --options runtime --timestamp --entitlements "$ENTITLEMENTS_FILE" --sign "$CODESIGN_IDENTITY" "$APP_PATH"

  # 验证签名
  echo "验证签名..."
  codesign --verify --verbose "$APP_PATH"

  # 详细验证签名
  echo "详细验证签名..."
  codesign --display --verbose=4 "$APP_PATH"
fi

# 公证应用
if [ "$ENABLE_CODESIGN" == "true" ] && [ "$ENABLE_NOTARIZE" == "true" ]; then
  echo "准备公证应用..."

  # 检查必要的环境变量
  if [ -z "$APPLE_ID" ] || [ -z "$APPLE_APP_SPECIFIC_PASSWORD" ] || [ -z "$APPLE_TEAM_ID" ]; then
    echo "警告: 缺少公证所需的环境变量 (APPLE_ID, APPLE_APP_SPECIFIC_PASSWORD, APPLE_TEAM_ID)"
    echo "跳过公证步骤"
  else
    # 输出公证参数
    echo "公证参数:"
    echo "  APPLE_ID: ${APPLE_ID}"
    echo "  TEAM_ID: ${APPLE_TEAM_ID}"
    echo "  APP_PATH: ${APP_PATH}"
  fi
fi

# 检查并安装 dmgbuild
echo "检查 dmgbuild 是否可用..."

# 检查Python是否可用
echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python 3，请安装Python 3后再运行此脚本"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 检查Python版本
PYTHON_VERSION=$(python3 --version | awk '{print $2}')
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)

echo "检测到Python版本: $PYTHON_VERSION"

# 检查Python版本是否满足要求（至少Python 3.6）
if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 6 ]); then
    echo "错误: dmgbuild需要Python 3.6或更高版本，当前版本为$PYTHON_VERSION"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 检查pip3是否可用
if ! command -v pip3 &> /dev/null; then
    echo "错误: 未找到pip3，请安装pip3后再运行此脚本"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 检查并安装 dmgbuild
echo "检查 dmgbuild 是否可用..."
if ! command -v dmgbuild &> /dev/null; then
    echo "dmgbuild 未安装，创建临时虚拟环境..."
    VENV_DIR="$TEMP_DIR/venv"
    python3 -m venv "$VENV_DIR"
    source "$VENV_DIR/bin/activate"
    pip install dmgbuild
    if [ $? -ne 0 ]; then
        echo "错误: 无法安装 dmgbuild"
        deactivate
        rm -rf "$TEMP_DIR"
        exit 1
    fi
    DMGBUILD_CMD="$VENV_DIR/bin/dmgbuild"
else
    DMGBUILD_CMD="dmgbuild"
fi

# 确保dmgbuild在PATH中
if ! command -v dmgbuild &> /dev/null; then
    echo "dmgbuild已安装但不在PATH中，尝试使用python模块直接运行..."
    DMGBUILD_CMD="python3 -m dmgbuild"
else
    DMGBUILD_CMD="dmgbuild"
fi


# 删除 __MACOSX 和 .DS_Store 文件
echo "删除APP_PATH父目录下所有 __MACOSX 和 .DS_Store 文件..."
find "$(dirname "$APP_PATH")" -name "__MACOSX" -type d -exec rm -rf {} +
find "$(dirname "$APP_PATH")" -name ".DS_Store" -type f -delete

# 创建临时 settings 文件
TEMP_SETTINGS="./temp_settings.py"
# 创建临时DMG
FINAL_DMG="$TEMP_DIR/Kwaipilot.dmg"

ls -la "$APP_PATH"
ls -la "$(dirname "$APP_PATH")"

# 删除 __MACOSX 和 .DS_Store 文件
echo "删除APP_PATH父目录下所有 __MACOSX 和 .DS_Store 文件..."
find "$(dirname "$APP_PATH")" -name "__MACOSX" -type d -exec rm -rf {} +
find "$(dirname "$APP_PATH")" -name ".DS_Store" -type f -delete

cat settings.py | sed -e "s|{{APP_PATH}}|$APP_PATH|g" -e "s|{{filename}}|$FINAL_DMG|g" > "$TEMP_SETTINGS"

# 使用dmgbuild创建DMG
echo "dmgbuild参数:"
echo "  卷名: Kwaipilot"
echo "  输出文件: $FINAL_DMG"
echo "  源文件: $APP_PATH"
echo "  设置文件: $TEMP_SETTINGS"

# 使用临时 settings 文件创建 DMG
echo "创建DMG..."
$DMGBUILD_CMD -s "$TEMP_SETTINGS" "Kwaipilot" "$FINAL_DMG"

# 签名DMG
if [ "$ENABLE_CODESIGN" == "true" ]; then
  echo "开始签名DMG文件..."
  codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" "$FINAL_DMG"
  if [ $? -ne 0 ]; then
      echo "错误: DMG签名失败"
      rm -rf "$TEMP_DIR"
      exit 1
  fi
  echo "DMG签名成功"
  codesign -vv "$FINAL_DMG"

  # 公证DMG
  if [ "$ENABLE_NOTARIZE" == "true" ] && [ ! -z "$APPLE_ID" ] && [ ! -z "$APPLE_APP_SPECIFIC_PASSWORD" ] && [ ! -z "$APPLE_TEAM_ID" ]; then
      echo "开始公证DMG文件..."
      NOTARY_DMG_OUTPUT="$TEMP_DIR/notary_dmg_output.txt"
      xcrun notarytool submit "$FINAL_DMG" --apple-id "$APPLE_ID" --password "$APPLE_APP_SPECIFIC_PASSWORD" --team-id "$APPLE_TEAM_ID" --wait > "$NOTARY_DMG_OUTPUT" 2>&1
      DMG_SUBMISSION_ID=$(awk '/^[ \t]*id:/ {print $2}' "$NOTARY_DMG_OUTPUT" | head -n 1 | tr -d '\n\r')
      DMG_NOTARY_STATUS=$(awk '/^[ \t]*status:/ {print $2}' "$NOTARY_DMG_OUTPUT" | head -n 1 | tr -d '\n\r')
      echo "DMG公证结果:"
      cat "$NOTARY_DMG_OUTPUT"
      echo "DMG提交ID: [${DMG_SUBMISSION_ID}]"
      echo "DMG状态: [${DMG_NOTARY_STATUS}]"

      if [ "$DMG_NOTARY_STATUS" != "Accepted" ]; then
          echo "DMG公证失败，获取详细日志..."
          DMG_ERROR_LOG="dmg_notary_error_$(date +%Y%m%d_%H%M%S).log"
          xcrun notarytool log "$DMG_SUBMISSION_ID" --apple-id "$APPLE_ID" --password "$APPLE_APP_SPECIFIC_PASSWORD" --team-id "$APPLE_TEAM_ID" > "./$DMG_ERROR_LOG" 2>&1
          echo "DMG错误日志已保存到: $DMG_ERROR_LOG"
          cat "./$DMG_ERROR_LOG"
          rm -rf "$TEMP_DIR"
          exit 1
      fi

      # 装订公证票据到DMG
      echo "装订公证票据到DMG..."
      xcrun stapler staple "$FINAL_DMG"
      if [ $? -ne 0 ]; then
          echo "警告: DMG装订公证票据失败"
      else
          echo "DMG装订公证票据成功"
      fi

      # 验证DMG公证状态
      echo "验证DMG公证状态..."
      spctl -a -vv -t open --context context:primary-signature "$FINAL_DMG"
  fi
fi

# 创建 dist 目录
mkdir -p dist

# 获取当前commit id
COMMIT_ID=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 移动DMG文件到dist目录,文件名包含平台、架构、commit id和时间戳
mv "$FINAL_DMG" "dist/Kwaipilot-${PLATFORM}-${ARCH}-${COMMIT_ID}-${TIMESTAMP}.dmg"


# ------------------------------ 为自动更新创建ZIP格式的应用包 ------------------------------
# 为自动更新创建ZIP格式的应用包
echo "为自动更新创建ZIP格式的应用包..."
ZIP_FILENAME="Kwaipilot-${PLATFORM}-${ARCH}-${COMMIT_ID}-${TIMESTAMP}.zip"
ZIP_PATH="dist/${ZIP_FILENAME}"

# 创建临时目录用于ZIP打包
ZIP_TEMP_DIR="$TEMP_DIR/zip_package"
mkdir -p "$ZIP_TEMP_DIR"

# 复制应用到临时目录
cp -R "$APP_PATH" "$ZIP_TEMP_DIR/"

# 清理扩展属性（避免ZIP中包含不必要的元数据）
xattr -cr "$ZIP_TEMP_DIR"

# 创建ZIP文件（使用与 electron autoUpdater 兼容的格式）
echo "创建ZIP文件: $ZIP_PATH"
# 确保dist目录存在
mkdir -p "$(dirname "$ZIP_PATH")"
# 使用绝对路径创建ZIP文件
ZIP_ABS_PATH="$(pwd)/$ZIP_PATH"
(cd "$ZIP_TEMP_DIR" && zip -r -y -q "$ZIP_ABS_PATH" *)

# 验证ZIP文件
echo "验证ZIP文件完整性..."
if unzip -t "$ZIP_ABS_PATH" > /dev/null 2>&1; then
    echo "✅ ZIP文件创建成功: $ZIP_FILENAME"
else
    echo "❌ ZIP文件创建失败"
    exit 1
fi

# 签名ZIP文件（如果启用了代码签名）
if [ "$ENABLE_CODESIGN" == "true" ]; then
    echo "签名ZIP文件..."
    codesign --force --options runtime --timestamp --sign "$CODESIGN_IDENTITY" "$ZIP_ABS_PATH"
    if [ $? -eq 0 ]; then
        echo "✅ ZIP文件签名成功"
        codesign -vv "$ZIP_ABS_PATH"
    else
        echo "⚠️ ZIP文件签名失败，但继续处理"
    fi
fi

# ------------------------------ 为自动更新创建ZIP格式的应用包 ------------------------------



# 生成更新信息的函数
function generate_update_json() {
    local ZIP_PATH="$1"  # 从参数传入 ZIP 文件的完整路径

    if [ -z "$ZIP_PATH" ]; then
        echo "错误: 缺少 ZIP 文件路径参数"
        echo "用法: generate_update_json <zip_file_path>"
        return 1
    fi

    # 从 product.json 读取版本号
    local VERSION=$(python3 -c "import json; print(json.load(open('product.json'))['appVersion'])" 2>/dev/null || echo "unknown")
    echo "从 product.json 读取到版本号: ${VERSION}"

    # 从 product.json 读取 quality 字段
    local QUALITY_VALUE=$(python3 -c "import json; print(json.load(open('product.json')).get('quality', 'dev'))" 2>/dev/null || echo "dev")
    echo "从 product.json 读取到 quality: ${QUALITY_VALUE}"

    # 验证 ZIP 文件是否存在
    if [ ! -f "${ZIP_PATH}" ]; then
        echo "错误: ZIP 文件不存在: ${ZIP_PATH}"
        return 1
    fi

    # 获取 ZIP 文件信息
    local ZIP_FILENAME=$(basename "${ZIP_PATH}")

    echo "正在为 ZIP 更新包生成配置信息..."

    # 计算 ZIP 文件哈希值
    echo "计算ZIP文件哈希值..."
    local ZIP_SHA1=$(shasum -a 1 "${ZIP_PATH}" | cut -d' ' -f1)
    local ZIP_SHA256=$(shasum -a 256 "${ZIP_PATH}" | cut -d' ' -f1)
    local ZIP_FILE_SIZE=$(stat -f%z "${ZIP_PATH}" 2>/dev/null || stat -c%s "${ZIP_PATH}" 2>/dev/null)
    local UPDATE_TIMESTAMP=$(date +%s)

    local DMG_FILENAME="${ZIP_FILENAME%.zip}.dmg"

    # 构建下载 URL (可以通过环境变量自定义)
    local UPDATE_SERVER_URL=${UPDATE_SERVER_URL:-"https://h3.static.yximgs.com/kos/nlav11354/ai-ide"}
    local ZIP_DOWNLOAD_URL="${UPDATE_SERVER_URL}/${ZIP_FILENAME}"
    local DMG_DOWNLOAD_URL="${UPDATE_SERVER_URL}/${DMG_FILENAME}"

    # 生成主更新配置文件（指向ZIP格式，用于自动更新）
    # 从ZIP文件名中去掉.zip扩展名，然后添加.json扩展名
    local MAIN_JSON_FILENAME="${ZIP_FILENAME%.zip}.json"
    local MAIN_JSON_PATH="dist/${MAIN_JSON_FILENAME}"
    local LONG_COMMIT_ID=$(git rev-parse HEAD)

    # 获取最近一次的commit message，用于release notes
    local COMMIT_MESSAGE=$(git log -1 --pretty=format:"%s" 2>/dev/null || echo "No commit message available")
    echo "获取到最近的commit message: ${COMMIT_MESSAGE}"

    # 获取当前时间，格式化为ISO 8601标准格式
    local CURRENT_DATE=$(date -u +"%Y-%m-%dT%H:%M:%S+00:00")
    echo "当前发布时间: ${CURRENT_DATE}"

    cat > "${MAIN_JSON_PATH}" << EOF
{
    "url": "${ZIP_DOWNLOAD_URL}",
    "version": "${VERSION}",
    "productVersion": "${VERSION}",
    "sha1hash": "${ZIP_SHA1}",
    "sha256hash": "${ZIP_SHA256}",
    "timestamp": ${UPDATE_TIMESTAMP},
    "fileSize": ${ZIP_FILE_SIZE},
    "filename": "${ZIP_FILENAME}",
    "architecture": "${ARCH}",
    "platform": "${PLATFORM}",
    "packageType": "zip",
    "dmgUrl": "${DMG_DOWNLOAD_URL}",
    "commitId": "${LONG_COMMIT_ID}",
    "name": "${VERSION}",
    "notes": "${COMMIT_MESSAGE}",
    "pub_date": "${CURRENT_DATE}",
    "quality": "${QUALITY_VALUE}"
}
EOF

    echo "✅ 更新配置已生成:"
    echo "   ZIP 文件: ${ZIP_FILENAME}"
    echo "   主配置:   ${MAIN_JSON_FILENAME}"
    echo "   ZIP 文件大小: ${ZIP_FILE_SIZE} bytes"
    echo "   ZIP SHA256: ${ZIP_SHA256}"

    # 上传文件到KCDN
    echo "上传文件到KCDN..."
    kcdn_upload "${ZIP_PATH}"
    kcdn_upload "${MAIN_JSON_PATH}"

    return 0
}

generate_update_json "$ZIP_ABS_PATH"

# 拆分：自动推送 json 到 kconf 逻辑移至 publish_kconf.sh
if [ "$AUTO_PUBLISH_JSON" == "true" ]; then
    # 计算平台 key
    if [ "$ARCH" == "x64" ]; then
        PLATFORM_KEY="$PLATFORM"
    else
        PLATFORM_KEY="$PLATFORM-$ARCH"
    fi
    # 计算 MAIN_JSON_PATH
    if [ -z "$MAIN_JSON_PATH" ]; then
        ZIP_BASENAME=$(basename "$ZIP_ABS_PATH" .zip)
        MAIN_JSON_PATH="dist/${ZIP_BASENAME}.json"
    fi
    # 确保 QUALITY_VALUE 有正确的值
    if [ -z "$QUALITY_VALUE" ]; then
        QUALITY_VALUE=$(python3 -c "import json; print(json.load(open('product.json')).get('quality', 'dev'))" 2>/dev/null || echo "dev")
        echo "重新读取 quality 值: ${QUALITY_VALUE}"
    fi
    ./publish_kconf.sh "$MAIN_JSON_PATH" "$PLATFORM_KEY" "$QUALITY_VALUE" "$KCONF_TOKEN"
fi

# 清理ZIP临时目录
rm -rf "$ZIP_TEMP_DIR"

# 清理
echo "清理临时文件..."
rm -f "$TEMP_SETTINGS"
rm -rf "$TEMP_DIR"

echo "处理完成! 签名后的文件: dist/Kwaipilot-${PLATFORM}-${ARCH}-${COMMIT_ID}-${TIMESTAMP}.dmg"
