import { useState, useEffect } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";

export function useAgentPreferenceConfig() {
  const [agentPreference, setAgentPreference] = useState(false);

  useEffect(() => {
    // 初始获取配置值
    kwaiPilotBridgeAPI
      .getConfig<string>("agentPreference")
      .then((data) => {
        setAgentPreference(data.value === "intelligent");
      })
      .catch(() => {
        // 如果获取配置失败，默认不显示
        setAgentPreference(false);
      });

    // 监听配置变化
    const subscription = kwaiPilotBridgeAPI.observableAPI.settingUpdate().subscribe((settingData) => {
      if (settingData.key === "agentPreference") {
        setAgentPreference(settingData.value === "intelligent");
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return agentPreference;
}
