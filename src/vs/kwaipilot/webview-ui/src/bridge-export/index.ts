// NOTE: ide bridge 实现类
import {
  UserInfo,
} from "@shared/types";
import {
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
} from "@shared/types/bridge";
import { KwaiPilotBridge, VSCodeNativeBridge } from "./kwaipilotBridge";
import { BridgeEventType } from "../bridge-share/types";
import {
  LoggerSupplementaryField,
} from "@shared/types/logger";
import { ObservableAPI } from "shared";
import { Observable } from "rxjs";
import { createWebviewRpcContext } from "../bridge-share/WebviewRpcContext";
import { logger } from "@/utils/logger";
import { VSCodeServicesAccessor } from "../mount-export";
import { setGlobalObject } from "@/utils/globalObject";
import { BaseKwaiPilotBridgeAPI, proxyExtensionAPI } from "../bridge-share/BaseKwaiPilotBridgeAPI";

export class KwaiPilotBridgeAPI extends BaseKwaiPilotBridgeAPI {
  protected loggerScope = "VSCodeKwaiPilotBridgeAPI";
  private servicesAccessor?: VSCodeServicesAccessor;

  constructor() {
    super();
    // 初始化bridge实例
    this.bridge = new KwaiPilotBridge();
    this.observableAPI = this.createObservableAPI();
  }

  setBridge(bridge: VSCodeNativeBridge) {
    // setupBridge 时机区分：ide 和插件
    this.setupBridge(bridge);
  }

  /**
   * 设置 VSCode services accessor
   * @param accessor VSCode services accessor 实例
   */
  setServicesAccessor(accessor: VSCodeServicesAccessor) {
    this.servicesAccessor = accessor;
  }

  /**
   * 获取 VSCode services accessor
   * @returns VSCode services accessor 实例或 undefined
   */
  getServicesAccessor(): VSCodeServicesAccessor | undefined {
    return this.servicesAccessor;
  }

  // 实现抽象方法：初始化RPC上下文
  protected initializeRpcContext(): void {
    this.rpcContext = createWebviewRpcContext({
      logger: () => logger,
      protocol: {
        send: (message) => {
          this.bridge.postOneWayMessage(
            NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE,
            message,
          );
        },
        onMessage: (listener) => {
          return this.addMessageListener(
            WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE,
            (data) => {
              listener(data);
            },
          );
        },
      },
    });
  }

  // 实现抽象方法：使用服务访问器获取用户信息
  public getAndWatchUserInfo(callback: (userinfo: UserInfo) => void) {
    this.servicesAccessor?.userInfoWatcherService?.getAndWatchUserInfo(callback);
  }

  // 实现抽象方法：创建Observable API
  protected createObservableAPI(): ObservableAPI {
    return {
      currentFileAndSelection: proxyExtensionAPI(this.bridge, "currentFileAndSelection"),
      visibility: proxyExtensionAPI(this.bridge, "visibility"),
      currentTheme: proxyExtensionAPI(this.bridge, "currentTheme"),
      isDeveloperMode: proxyExtensionAPI(this.bridge, "isDeveloperMode"),
      latestCopiedContent: proxyExtensionAPI(this.bridge, "latestCopiedContent"),
      indexState: proxyExtensionAPI(this.bridge, "indexState"),
      mcpServers: proxyExtensionAPI(this.bridge, "mcpServers"),
      customPanelPage: proxyExtensionAPI(this.bridge, "customPanelPage"),
      rulesList: proxyExtensionAPI(this.bridge, "rulesList"),
      settingUpdate: proxyExtensionAPI(this.bridge, "settingUpdate"),
      userInfo: () => {
        return new Observable<UserInfo | undefined>((observer) => {
          const servicesAccessor = this.getServicesAccessor();

          if (!servicesAccessor?.userInfoWatcherService) {
            // 如果服务不可用，发送 undefined 并完成
            observer.next(undefined);
            observer.complete();
            return;
          }

          // 创建回调函数
          const callback = (userInfo: UserInfo | undefined) => {
            observer.next(userInfo);
          };

          // 开始监听用户信息变化
          servicesAccessor.userInfoWatcherService.getAndWatchUserInfo(callback);

          // 返回清理函数
          return () => {
            if (servicesAccessor?.userInfoWatcherService?.removeUserInfoWatcher) {
              servicesAccessor.userInfoWatcherService.removeUserInfoWatcher(callback);
            }
          };
        });
      },
    };
  }

  // 重写打印日志方法以保持兼容性
  public printLogger(data: {
    level: "silly" | "debug" | "verbose" | "info" | "warn" | "error";
    msg: string;
    scope: string;
    tags?: LoggerSupplementaryField;
  }) {
    this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.PRINT_LOGGER, {
      id: this.bridge.generateId(),
      event: BridgeEventType.PRINT_LOGGER,
      payload: data,
    });
  }

  get editor() {
    // 获取父类的 editor 对象
    const parentEditor = super.editor;

    return {
      ...parentEditor,
      // 只覆盖需要修改的方法
      keepDiff: (payload?: { filepath?: string; abortChat?: boolean }) => {
        this.servicesAccessor?.editCodeService?.keepDiffExport({ filepath: payload?.filepath, abortChat: payload?.abortChat });
        const partialPaths = this.servicesAccessor?.editCodeService?.getPartialAcceptPathsExport();
        return new Promise((resolve) => {
          this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_FEEDBACK, {
            id: this.bridge.generateId(),
            event: BridgeEventType.COMPOSER_DIFF_FEEDBACK,
            payload: { type: "keep", partialPaths, ...payload },
          }, resolve);
        });
      },
      undoDiff: (payload?: { filepath?: string; abortChat?: boolean }) => {
        this.servicesAccessor?.editCodeService?.undoDiffExport({ filepath: payload?.filepath, abortChat: payload?.abortChat });
        const partialPaths = this.servicesAccessor?.editCodeService?.getPartialRejectPathsExport() || [];
        return new Promise((resolve) => {
          this.bridge.postMessage(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_FEEDBACK, {
            id: this.bridge.generateId(),
            event: BridgeEventType.COMPOSER_DIFF_FEEDBACK,
            payload: { type: "undo", partialPaths, ...payload },
          }, resolve);
        });
      },

      clearAllDiffState: () => {
        this.servicesAccessor?.editCodeService?.resetAllDiffsExport();
      },
    };
  }
}

export const kwaiPilotBridgeAPI = new KwaiPilotBridgeAPI();

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
setGlobalObject("kwaiPilotBridgeAPI", kwaiPilotBridgeAPI);
