import { create } from "zustand";
import { EditorConfig } from "@shared/types";

export interface EditorConfigState {
  editorConfig: EditorConfig;
  setEditorConfig: (editorConfig: EditorConfig) => void;
  setCurrentThemeName: (themeName: string) => void;
  currentThemeName: string;
}

export const useVsEditorConfig = create<EditorConfigState>()(set => ({
  editorConfig: {
    fontSize: 14,
    fontFamily: "PingFangSC, PingFangSC-Regular",
    tabSize: 4,
    theme: "dark-plus",
  },
  currentThemeName: "kwaipilot-default-theme-dark",

  setEditorConfig(editorConfig) {
    set({
      editorConfig,
    });
  },

  setCurrentThemeName(themeName: string) {
    set({
      currentThemeName: themeName,
    });
  },
}));
