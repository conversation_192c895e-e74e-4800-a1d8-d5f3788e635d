import { AutoThinkInfo, ICachedMessageAnswer, QAItem } from "@shared/types/chatHistory";
import { Icon } from "@/components/Union/t-iconify";
import { useRecordStore } from "@/store/record";
import { useCallback, useEffect, useMemo } from "react";
import { useBoolean } from "react-use";
import { GlowingText } from "@/components/ui/GlowingText";
import ReactMarkdown from "react-markdown";
import { rehypePlugins } from "@/utils/markdown";
import { RemarkGfm } from "@kwaipilot/markdown-render";
import clsx from "clsx";
import style from "./MarkdownThinkRender.module.less";

export function MarkdownThinkRender({
  answer, thinkInfo,
}: { qaItem: QAItem;answer: ICachedMessageAnswer; thinkInfo: AutoThinkInfo }) {
  const [thinkContentExpanded, toggleThinkContent] = useBoolean(false);

  const [userOperated, setUserOperated] = useBoolean(false);

  const {
    judge,
    think,
    thinkOn,
    content,
    cost,
  } = thinkInfo;

  const loadingStatu = useRecordStore(state => state.loadingStatu);

  // 当前加载中的数据是否和我有关？
  const isLoading = !!(
    loadingStatu
    && loadingStatu.id === answer.id
    && loadingStatu.status === "loading"
  );

  const handleCollapseTitleClick = useCallback(() => {
    toggleThinkContent();
    setUserOperated(true);
  }, [toggleThinkContent, setUserOperated]);

  useEffect(() => {
    if (isLoading) {
      // 加载时自动展开
      toggleThinkContent(true);
    }
  }, [isLoading, toggleThinkContent]);

  useEffect(() => {
    if (isLoading && thinkContentExpanded && content && !userOperated) {
      // 如果正在加载中且内容已经返回，则自动收起思考内容
      toggleThinkContent(false);
    }
  }, [content, isLoading, thinkContentExpanded, toggleThinkContent, userOperated]);

  const useGlowingEffect = Boolean(isLoading && !content); // 生成中 & 正式内容还没有返回

  const collapseTitle = useMemo(() => {
    const thinkOnFlag = (
      <div className="h-[18px] rounded-[9px] inline-flex items-center gap-[2px] text-disabledForeground text-[9px] leading-[18px] whitespace-nowrap px-1 bg-textCodeBlock-background">
        <span className=" w-[3px] h-[3px] bg-charts-green rounded-full"></span>
        <span>已开启思考模式</span>
      </div>
    );
    if (isLoading && !content) {
      if (thinkOn === "unset") {
        return "分析中...";
      }
      else {
        return (
          <div className="flex items-center gap-[10px]">
            思考中...
            {thinkOnFlag}
          </div>
        );
      }
    }
    else {
      return (
        <div className="flex items-center gap-[10px]">
          思考过程(
          {Math.round(cost / 1000)}
          s)
          {thinkOnFlag}
        </div>
      );
    }
  }, [content, cost, isLoading, thinkOn]);

  if (thinkOn === "off") {
    return null;
  }
  return (
    <>
      <div className="text-[12px] text-descriptionForeground">
        <div className=" flex items-center gap-1 mb-[4px] cursor-pointer  text-descriptionForeground hover:text-foreground group" onClick={handleCollapseTitleClick}>
          <Icon icon="codicon:chevron-down" className={thinkContentExpanded ? "" : "hidden"} width={12}></Icon>
          <Icon icon="codicon:sparkle" className={thinkContentExpanded ? "hidden" : " group-hover:hidden"} width="12" height="12" />
          <Icon icon="codicon:chevron-right" className={thinkContentExpanded ? "hidden" : " group-hover:block hidden"} width="12" height="12" />
          {useGlowingEffect
            ? (
                <GlowingText>
                  {collapseTitle}
                </GlowingText>
              )
            : (
                <span>
                  {collapseTitle}
                </span>
              )}
        </div>
        {thinkContentExpanded && (
          <>
            <div className={clsx(
              " before:w-[1px] box-border before:content-[''] pl-4 relative before:absolute before:left-[5px] before:h-full before:bg-radio-inactiveBorder",
              style["kwaipilot-markdown"])}
            >
              <ReactMarkdown
                remarkPlugins={[RemarkGfm]}
                rehypePlugins={rehypePlugins(true) as any}
                children={think || judge}
              />
            </div>
          </>
        )}
      </div>

    </>
  );
}
