/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import { Dispatch, forwardRef, SetStateAction, useMemo, type JSX } from "react";

import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  MenuRenderFn,
  MenuResolution,
  MenuTextMatch,
  useBasicTypeaheadTriggerMatch,
} from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { $createTextNode, $getRoot, $getSelection, $insertNodes, $isRangeSelection, COMMAND_PRIORITY_LOW, COMMAND_PRIORITY_NORMAL, KEY_BACKSPACE_COMMAND, KEY_ESCAPE_COMMAND, LexicalEditor, PASTE_COMMAND, TextNode } from "lexical";
import { useCallback, useEffect, useState } from "react";
import { $createMentionNodeV2 } from "./MentionNodeV2/MentionNodeV2";
import { MentionPanel } from "@/logics/UserInputTextarea/MentionPanel/MentionPanel";
import { autoUpdate, flip, shift, useFloating } from "@floating-ui/react-dom";
import { MentionTypeaheadOption, TypeaheadMenuOptionType, useMentionOptions } from "@/logics/UserInputTextarea/MentionPanel/useOptions";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useLatest } from "react-use";
import { MentionNodeV2Structure_File, MentionNodeV2Structure_Knowledge, MentionNodeV2Structure_Rule, MentionNodeV2Structure_Selection, MentionNodeV2Structure_Tree, placeholderMentionNodeV2Structure_Codebase, placeholderMentionNodeV2Structure_Web } from "shared/lib/MentionNodeV2/nodes";
import { useUserInputTextAreaContext } from "@/logics/UserInputTextarea/UserInputTextAreaContext";
import { throwNeverError } from "@/utils/throwUnknownError";
import { useRichEditorContext } from "../hooks/useRichEditorContext";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { useContextHeaderContext } from "@/logics/UserInputTextarea/ContextHeader/ContextHeaderContext";
import { Portal } from "@/components/Union/chakra-ui";
import { DOM } from "@/utils/dom";
import { TypeaheadMenuContext } from "./MentionsV2PluginContext";
import { BetterLexicalTypeaheadMenuPlugin } from "./BetterLexicalTypeaheadMenuPlugin";
import { FileData } from "shared/lib/misc/file";
import { useAgentPreferenceConfig } from "@/hooks/useAgentPreferenceConfig";

/**
 * 输入框字符匹配
 */
export function createMatcher(triggers: string[]) {
  const PUNCTUATION
  = "\\.,\\+\\*\\?\\$\\@\\|#{}\\(\\)\\^\\-\\[\\]\\\\/!%'\"~=<>_:;";

  const TRIGGERS = triggers.join("");

  // Chars we expect to see in a mention (non-space, non-punctuation).
  const VALID_CHARS = "[^" + TRIGGERS + PUNCTUATION + "\\s]";

  // Non-standard series of chars. Each series must be preceded and followed by
  // a valid char.
  const VALID_JOINS
  = "(?:"
  + "\\.[ |$]|" // E.g. "r. " in "Mr. Smith"
  + " |" // E.g. " " in "Josh Duck"
  + "["
  + PUNCTUATION
  + "]|" // E.g. "-' in "Salier-Hellendag"
  + ")";

  const LENGTH_LIMIT = 75;

  const AtSignMentionsRegex = new RegExp(
    "(^|\\s|\\()("
    + "["
    + TRIGGERS
    + "]"
    + "((?:"
    + VALID_CHARS
    + VALID_JOINS
    + "){0,"
    + LENGTH_LIMIT
    + "})"
    + ")$",
  );

  // 50 is the longest alias length limit.
  const ALIAS_LENGTH_LIMIT = 50;

  // Regex used to match alias.
  const AtSignMentionsRegexAliasRegex = new RegExp(
    "(^|\\s|\\()("
    + "["
    + TRIGGERS
    + "]"
    + "((?:"
    + VALID_CHARS
    + "){0,"
    + ALIAS_LENGTH_LIMIT
    + "})"
    + ")$",
  );

  function checkForAtSignMentions(
    text: string,
    minMatchLength: number,
  ): MenuTextMatch | null {
    let match = AtSignMentionsRegex.exec(text);

    if (match === null) {
      match = AtSignMentionsRegexAliasRegex.exec(text);
    }
    if (match !== null) {
    // The strategy ignores leading whitespace but we need to know it's
    // length to add it to the leadOffset
      const maybeLeadingWhitespace = match[1];

      const matchingString = match[3];
      if (matchingString.length >= minMatchLength) {
        return {
          leadOffset: match.index + maybeLeadingWhitespace.length,
          matchingString,
          replaceableString: match[2],
        };
      }
    }
    return null;
  }

  function getPossibleQueryMatch(text: string): MenuTextMatch | null {
    return checkForAtSignMentions(text, /* 在按下# 立即触发 */0);
  }
  return {
    getPossibleQueryMatch,
  };
}

const mentionPanelMatcher = createMatcher(["#"]);

function isCursorAtStartingPosition(editor: LexicalEditor) {
  return editor.getEditorState().read(() => {
    const selection = $getSelection(); // 获取当前选择的内容
    if (!$isRangeSelection(selection)) {
      return false;
    }
    const anchor = selection.anchor;
    const focus = selection.focus;
    if (anchor.key !== focus.key || anchor.offset !== focus.offset) {
      return false;
    }
    const anchorNode = anchor.getNode();
    if (anchor.offset !== 0) return false;
    if (anchorNode === $getRoot().getFirstChild()) return true;
    let v = anchorNode.getPreviousSibling(); // 获取当前节点的前一个兄弟节点
    while (v) {
      if (v.getTextContent().trim() !== "") {
        return false;
      }
      v = v.getPreviousSibling(); // 继续获取下一个前一个兄弟节点
    }
    return true;
  });
}

export interface TypeaheadMenuState {
  anchorElementRef: Parameters<MenuRenderFn<MentionTypeaheadOption>>[0];
  itemProps: Parameters<MenuRenderFn<MentionTypeaheadOption>>[1];
  matchingString: Parameters<MenuRenderFn<MentionTypeaheadOption>>[2];
  currentMenu: TypeaheadMenuOptionType;
  setCurrentMenu: Dispatch<SetStateAction<TypeaheadMenuOptionType>>;
}

/**
 * 可以被当做 currentMenu 的类型
 */
const ALL_MENU_TYPES = [TypeaheadMenuOptionType.file, TypeaheadMenuOptionType.folder, TypeaheadMenuOptionType.rule, TypeaheadMenuOptionType.none, TypeaheadMenuOptionType.knowledge];

export function MenuRenderPortal({
  role,
  menuResolution,
  anchorElementRef,
  children,
}: {
  role: "bottom" | "conversation";
  menuResolution: MenuResolution | undefined;
  anchorElementRef: Parameters<MenuRenderFn<MentionTypeaheadOption>>[0];
  children?: React.ReactNode;
}) {
  const { refs, floatingStyles } = useFloating({
    placement: role === "bottom" ? "top-start" : "bottom-start",
    middleware: [
      flip(),
      shift(),
    ],
    whileElementsMounted: (reference, floating, update) => {
      return autoUpdate(reference, floating, update);
    },
  });
  useEffect(() => {
    const anchor = anchorElementRef.current;
    if (anchor) {
      /*
        修复滚动问题, lexical 实现中,anchor 实际会在# 字符的下方,当 textarea scroll 时 可能不可见,
        所以需要通过这种方式把他的位置重置一下
         https://team.corp.kuaishou.com/task/********
          */
      anchor.style.maxHeight = "0px";
    }
    const ANCHORE_HEIGHT = 20;
    // 原生 anchor 在@文字下方，这里reset 到原始@文字的位置
    /**
       * 为什么要根据 menuResolution 定位, 而不是 anchorElement?
       *
       * lexical 的 anchorElement 实际会在# 字符的下方, 如果在输入框有滚动(或在最底下一行输入#触发)时,就会导致这里的代码被错误执行,从而变成错误的弹出位置
       * https://github.com/facebook/lexical/blob/a51b69d2b0808002a493172172ee0b8308db4c4c/packages/lexical-react/src/shared/LexicalMenu.ts#L554
       *
       * 也可以参考 team 复现
       * https://team.corp.kuaishou.com/task/B2492511
       *
       * 而menuResolution 的位置是保持在 # 中的
       */
    refs.setReference(menuResolution && anchor
      ? {
          getBoundingClientRect() {
            const anchorRect = menuResolution.getRect();
            const modifiedRect = {
              top: anchorRect.top,
              width: anchorRect.width,
              height: ANCHORE_HEIGHT,
              left: anchorRect.left,
              right: anchorRect.right,
              bottom: anchorRect.bottom + ANCHORE_HEIGHT,
              x: anchorRect.x,
              y: anchorRect.y,

            };
            return {
              ...modifiedRect,
              toJSON() {
                return JSON.stringify(modifiedRect);
              },
            };
          },
          getClientRects: () => anchor.getClientRects(),
          contextElement: anchor,
        }
      : null);
  }, [refs, anchorElementRef, menuResolution]);

  return anchorElementRef.current
    ? (
        <Portal>
          <div ref={refs.setFloating} style={floatingStyles} className="z-10">
            {children}
          </div>
        </Portal>
      )
    : null;
}

export const MentionsV2Plugin = forwardRef<JSX.Element | null, { mode: "chat" | "composer" }>(({ mode }, _ref) => {
  const [editor] = useLexicalComposerContext();

  const [queryString, setQueryString] = useState<string | null>(null);

  const checkForSlashTriggerMatch = useBasicTypeaheadTriggerMatch("/", {
    minLength: 0,
  });

  const { state: contextHeaderState } = useContextHeaderContext();
  const { setMentionShown, mentionShown } = useRichEditorContext();

  const [currentMenu, setCurrentMenu] = useState<TypeaheadMenuOptionType>(TypeaheadMenuOptionType.none);
  const allowImage = useAgentPreferenceConfig();

  const onSelectOption = useCallback(
    (
      menu: MentionTypeaheadOption,
      nodeToReplace: TextNode | null,
      closeMenu: () => void,
    ) => {
      if (menu.type === TypeaheadMenuOptionType.heading) {
        const targetMenu = ALL_MENU_TYPES.find(type => type === menu.name);
        if (targetMenu) {
          setCurrentMenu(targetMenu);
        }
      }
      else if (menu.type === TypeaheadMenuOptionType.customPrompt || menu.type === TypeaheadMenuOptionType.slashCommand) {
        // 不会有这种类型
      }
      else if (menu.type === TypeaheadMenuOptionType.file) {
        editor.update(() => {
          const structure = menu.structure as MentionNodeV2Structure_File;
          const mentionNode = $createMentionNodeV2({
            structure,
          });
          if (nodeToReplace) {
            nodeToReplace.replace(mentionNode);
            mentionNode.insertAfter($createTextNode(" "));
          }
          contextHeaderState.tryInsertNode({
            structure: {
              type: TypeaheadMenuOptionType.file,
              uri: structure.uri,
              relativePath: structure.relativePath,
            },
            followActiveEditor: false,
            isVirtualContext: true,
          }, {
            source: "textarea",
          });
        });
        closeMenu();
      }
      else if (menu.type === TypeaheadMenuOptionType.folder) {
        editor.update(() => {
          const structure = menu.structure as MentionNodeV2Structure_Tree;
          const mentionNode = $createMentionNodeV2({
            structure,
          });
          if (nodeToReplace) {
            nodeToReplace.replace(mentionNode);
            mentionNode.insertAfter($createTextNode(" "));
          }

          contextHeaderState.tryInsertNode({
            structure: {
              type: "tree",
              uri: structure.uri,
              relativePath: structure.relativePath,
            },
            followActiveEditor: false,
            isVirtualContext: true,
          }, {
            source: "textarea",
          });
        });
        closeMenu();
      }
      else if (menu.type === TypeaheadMenuOptionType.rule) {
        editor.update(() => {
          const structure = menu.structure as MentionNodeV2Structure_Rule;
          const mentionNode = $createMentionNodeV2({
            structure,
          });
          if (nodeToReplace) {
            nodeToReplace.replace(mentionNode);
            mentionNode.insertAfter($createTextNode(" "));
          }

          contextHeaderState.tryInsertNode({
            structure: {
              type: "rule",
              uri: structure.uri,
              relativePath: structure.relativePath,
            },
            followActiveEditor: false,
            isVirtualContext: true,
          }, {
            source: "textarea",
          });
        });
        closeMenu();
      }
      else if (menu.type === TypeaheadMenuOptionType.none) {
        throw new Error("not implemented");
      }
      else if (menu.type === TypeaheadMenuOptionType.header) {
        setCurrentMenu(TypeaheadMenuOptionType.none);
      }
      else if (menu.type === TypeaheadMenuOptionType.addRule) {
        kwaiPilotBridgeAPI.extensionComposer.$addRuleFile();
        closeMenu();
      }
      else if (menu.type === TypeaheadMenuOptionType.web) {
        editor.update(() => {
          const mentionNode = $createMentionNodeV2({
            structure: placeholderMentionNodeV2Structure_Web,
          });
          if (nodeToReplace) {
            nodeToReplace.replace(mentionNode);
            mentionNode.insertAfter($createTextNode(" "));
          }

          contextHeaderState.tryInsertNode({
            structure: placeholderMentionNodeV2Structure_Web,
            followActiveEditor: false,
            isVirtualContext: true,
          }, {
            source: "textarea",
          });
        });
        closeMenu();
      }
      else if (menu.type === TypeaheadMenuOptionType.codebase) {
        editor.update(() => {
          const mentionNode = $createMentionNodeV2({
            structure: placeholderMentionNodeV2Structure_Codebase,
          });
          if (nodeToReplace) {
            nodeToReplace.replace(mentionNode);
            mentionNode.insertAfter($createTextNode(" "));
          }

          contextHeaderState.tryInsertNode({
            structure: placeholderMentionNodeV2Structure_Codebase,
            followActiveEditor: false,
            isVirtualContext: true,
          }, {
            source: "textarea",
          });
        });
        closeMenu();
      }
      else if (menu.type === TypeaheadMenuOptionType.knowledge) {
        editor.update(() => {
          const structure = menu.structure as MentionNodeV2Structure_Knowledge;
          const mentionNode = $createMentionNodeV2({
            structure,
          });
          if (nodeToReplace) {
            nodeToReplace.replace(mentionNode);
            mentionNode.insertAfter($createTextNode(" "));
          }

          contextHeaderState.tryInsertNode({
            structure,
            followActiveEditor: false,
            isVirtualContext: true,
          }, {
            source: "textarea",
          });
        });
        closeMenu();
      }
      else {
        throwNeverError(menu.type);
      }
    },
    [editor, contextHeaderState],
  );

  const checkForMentionMatch = useCallback(
    (text: string) => {
      const slashMatch = checkForSlashTriggerMatch(text, editor);
      if (slashMatch !== null) {
        return null;
      }
      return mentionPanelMatcher.getPossibleQueryMatch(text);
    },
    [checkForSlashTriggerMatch, editor],
  );

  const { options: _options } = useMentionOptions({
    currentMenu,
    queryString: queryString || "",
    mode,
  });
  // TODO: 等服务端支持多选后放开
  const options = useMemo(() => _options.filter(o =>
    !(o.type === TypeaheadMenuOptionType.heading && o.name === TypeaheadMenuOptionType.knowledge)
    && !(o.type === TypeaheadMenuOptionType.knowledge),
  ),
  [_options],
  );

  const { role } = useUserInputTextAreaContext();

  const [menuResolution, setMenuResolution] = useState<MenuResolution>();

  const menuRenderFn: MenuRenderFn<MentionTypeaheadOption> = useCallback(function MenuRenderComponent(
    anchorElementRef,
    itemProps,
    matchingString,
  ) {
    return (
      <MenuRenderPortal
        role={role}
        menuResolution={menuResolution}
        anchorElementRef={anchorElementRef}
      >
        <TypeaheadMenuContext.Provider value={{ anchorElementRef, itemProps, matchingString, setCurrentMenu, currentMenu }}>
          <div className="typeahead-popover mentions-menu" tabIndex={-1}>
            <MentionPanel
              selectMode="any"
              options={options}
              query={queryString || ""}
            />
          </div>
        </TypeaheadMenuContext.Provider>
      </MenuRenderPortal>
    );
  }, [currentMenu, menuResolution, options, queryString, role]);

  const latestCopiedContent = useBridgeObservableAPI("latestCopiedContent");

  const [filesToProcess, setFilesToProcess] = useState<Promise<FileData>[]>([]);

  useEffect(() => {
    if (filesToProcess.length > 0) {
      Promise.all(filesToProcess)
        .then((files) => {
          const limit = 10 - (contextHeaderState.nodes.filter(node => node.structure.type === "remoteImage").length || 0);
          return kwaiPilotBridgeAPI.extensionMisc.$uploadImageDirectly(files as any, limit);
        })
        .then((uploadFiles) => {
          uploadFiles.forEach((file) => {
            contextHeaderState.tryInsertNode({
              structure: {
                type: "remoteImage",
                uri: file.uri,
                relativePath: file.relativePath,
                uploadInfo: file.uploadInfo,
              },
              isVirtualContext: false,
              followActiveEditor: false,
            }, {
              source: "context",
            });
          });
        })
        .catch((error) => {
          console.error("Failed to process files:", error);
        })
        .finally(() => {
          setFilesToProcess([]);
        });
    }
  }, [filesToProcess, contextHeaderState]);

  useEffect(() => {
    return editor.registerCommand(
      PASTE_COMMAND,
      (event: ClipboardEvent) => {
        // 对比 clipboard 内容和 vscode copy 存储的，如果一致，就认定是 vscode copy
        const clipboardContent = event.clipboardData?.getData("text/plain");
        const vscodeCopyContent = latestCopiedContent?.plainText;
        if (clipboardContent === vscodeCopyContent && latestCopiedContent &&/* 多行文本 */ clipboardContent?.includes("\n")) {
          editor.update(() => {
            const structure: MentionNodeV2Structure_Selection = {
              type: "selection",
              range: latestCopiedContent.ranges[0],
              content: latestCopiedContent.plainText,
              uri: latestCopiedContent.documentUri,
              relativePath: latestCopiedContent.relativePath,
            };
            const node = $createMentionNodeV2({ structure });

            $insertNodes([node, $createTextNode(" ")]);

            contextHeaderState.tryInsertNode({
              structure,
              followActiveEditor: false,
              isVirtualContext: true,
            }, {
              source: "clipboard",
            });
          });
          event.preventDefault();
          return true;
        }
        // 检查是否为截图文件
        if (mode === "composer" && allowImage) {
          const items = event.clipboardData?.items;
          if (items) {
            const filePromises: Promise<FileData>[] = [];

            for (let i = 0; i < items.length; i++) {
              const item = items[i];
              if (item.type.startsWith("image/")) {
                const file = item.getAsFile();
                if (file) {
                  const filePromise = file.arrayBuffer().then(arrayBuffer => ({
                    name: file.name,
                    type: file.type,
                    data: arrayBuffer,
                  }));
                  filePromises.push(filePromise);
                }
              }
            }

            if (filePromises.length > 0) {
            // 立即返回 true 以阻止默认粘贴行为
              event.preventDefault();

              // 设置要处理的文件
              setFilesToProcess(filePromises);

              return true;
            }
          }
        }
        return false;
      },
      COMMAND_PRIORITY_LOW,
    );
  }, [editor, contextHeaderState, latestCopiedContent, allowImage, mode]);

  useEffect(() => {
    return editor.registerCommand(
      KEY_BACKSPACE_COMMAND,
      () => {
        if (contextHeaderState.nodes.length && isCursorAtStartingPosition(editor)) {
          contextHeaderState.setNodes(prev => prev.slice(0, -1));
          contextHeaderState.markAsModified();
          return true;
        }
        return false;
      },
      COMMAND_PRIORITY_LOW,
    );
  });

  useEffect(() => {
    return editor.registerCommand(
      KEY_ESCAPE_COMMAND,
      () => {
        if (currentMenu !== TypeaheadMenuOptionType.none) {
          setCurrentMenu(TypeaheadMenuOptionType.none);
          return true;
        }
        return false;
      },
      COMMAND_PRIORITY_NORMAL,
    );
  }, [currentMenu, editor]);

  const mentionShownLatest = useLatest(mentionShown);

  useEffect(() => {
    return editor.registerRootListener((root) => {
      let listenerOutdated = false;
      const onBlur = () => {
        setTimeout(() => {
          if (!mentionShownLatest.current) {
            return;
          }
          if (listenerOutdated) {
            return;
          }
          const activeElement = DOM.getActiveElement();
          /*
          点击了编辑器和 MentionPanel 以外的元素
          https://team.corp.kuaishou.com/task/B2490391
           */
          const isClickedOutside = !(activeElement instanceof HTMLElement && activeElement.closest(".typeahead-popover"));
          if (isClickedOutside) {
            setCurrentMenu(TypeaheadMenuOptionType.none);
            const escEvent = new KeyboardEvent("keydown", {
              key: "Escape", // 按键名称
              code: "Escape", // 按键代码
              keyCode: 27, // 旧版浏览器中使用的键码
              which: 27, // 旧版浏览器中使用的键码
              bubbles: true, // 事件是否冒泡
              cancelable: true, // 事件是否可取消
            });
            setCurrentMenu(TypeaheadMenuOptionType.none);
            /* 手动触发 Mention 面板关闭 https://github.com/facebook/lexical/blob/v0.17.1/packages/lexical-react/src/shared/LexicalMenu.ts#L405 */
            editor.dispatchCommand(KEY_ESCAPE_COMMAND, escEvent);
          }
        }, 0);
      };
      root?.addEventListener("blur", onBlur);
      return () => {
        listenerOutdated = true;
        root?.removeEventListener("blur", onBlur);
      };
    });
  }, [editor, mentionShown, mentionShownLatest, setCurrentMenu]);

  return (
    <BetterLexicalTypeaheadMenuPlugin<MentionTypeaheadOption>
      onQueryChange={setQueryString}
      onSelectOption={onSelectOption}
      triggerFn={checkForMentionMatch}
      options={options}
      anchorClassName=" z-20"
      menuRenderFn={menuRenderFn}
      onOpen={(resolution) => {
        setMenuResolution(resolution);
        setMentionShown(true);
      }}
      onClose={() => {
        setMentionShown(false);
        setCurrentMenu(TypeaheadMenuOptionType.none);
      }}
    />
  );
});
