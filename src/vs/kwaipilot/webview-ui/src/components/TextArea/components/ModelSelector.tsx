import { CustomScrollBar } from "@/components/CustomScrollbar";
import { selectActiveModel, useRecordStore } from "@/store/record";
import { Popover, Tooltip } from "@/components/Union/chakra-ui";
import { PlacementWithLogical, PopoverContent, PopoverTrigger, useDisclosure } from "@chakra-ui/react";
import clsx from "clsx";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { IChatModelType, Model } from "@shared/types/business";
import { Icon } from "@/components/Union/t-iconify";

type ModelSelectorProps = {
  placement: PlacementWithLogical;
  children?: React.ReactNode;
  trigger?: "click" | "hover";
  selectModelCallback?: (modelType: IChatModelType) => void;
  className?: string;
  activeBgColor?: string;
};

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  placement,
  children,
  trigger,
  selectModelCallback,
  className,
}: ModelSelectorProps) => {
  const { onOpen, onClose, onToggle, isOpen } = useDisclosure();
  const chatModelDetail = useRecordStore(selectActiveModel);
  const modelList = useRecordStore(state => state.modelList);
  const setChatModelType = useRecordStore(state => state.setChatModelType);
  const selectModel = (model: Model) => {
    const prams: ReportOpt<"chSetting"> = {
      key: "chSetting",
      type: "modelType",
      content: model.modelType.toString(),
    };
    reportUserAction(prams);
    setChatModelType(model.modelType);
  };
  const handleSelect = (model: Model) => {
    if (model.disabled) {
      return;
    }
    const modelType = model.modelType;
    if (selectModelCallback) {
      selectModelCallback(modelType);
    }
    else {
      selectModel(model);
    }
    onClose();
  };

  /** 当前使用的模型 */

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      strategy="fixed"
      placement={placement}
      closeOnBlur={true}
      trigger={trigger}
      boundary="scrollParent"
      computePositionOnMount={true}
      eventListeners={true}
      flip={true}
    >
      <PopoverTrigger>
        <div
          onMouseDown={e => e.preventDefault()}
          className={clsx(
            "flex-auto overflow-hidden flex gap-1 items-center cursor-pointer rounded group",
            className,
          )}
          onClick={onToggle}
        >
          {children || (
            <>
              <div
                className={clsx(
                  " flex items-center gap-1 justify-center transition-all  text-foreground",
                )}
              >

                <span className=" text-xs">{chatModelDetail?.name}</span>
                <Icon icon="codicon:chevron-down" className={isOpen ? "rotate-180" : ""} width="14" height="14" />
              </div>

            </>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent
        border="none"
        w="200px"
        sx={{
          "&:focus": {
            outline: "none",
          },
          "&:focus-visible": {
            outline: "none",
            boxShadow: "none",
          },
        }}
      >
        <CustomScrollBar
          suppressScrollX
          className="h-[224px] w-[280px] border border-settings-dropdownBorder rounded"
        >
          <div className="p-1 w-full flex flex-col gap-1 rounded bg-editor-background text-dropdown-foreground">
            {modelList.map((model, idx) => {
              return (
                <div
                  className={clsx(
                    "w-full rounded-sm px-3 py-2 relative hover:bg-list-dropBackground",
                    model.disabled ? "cursor-not-allowed" : "cursor-pointer",
                  )}
                  onClick={() => handleSelect(model)}
                  key={idx}
                >
                  <Tooltip label={model.tooltip}>
                    <div className="flex gap-1 items-center">
                      <img src={model.icon} className="w-[14px] h-[14px]" />
                      <div
                        className={clsx(" leading-[19.5px] text-[13px]", [
                          model.disabled
                            ? "text-text-common-disable"
                            : "",
                        ])}
                      >
                        {model.name}
                      </div>
                      {model.vipIcon && <img src={model.vipIcon} alt="" />}
                    </div>
                  </Tooltip>
                  <div
                    className={clsx(
                      "text-[12px] leading-[18px]",
                      model.disabled
                        ? " text-menu-foreground"
                        : "",
                    )}
                  >
                    {model.desc}
                  </div>
                  { chatModelDetail.modelType === model.modelType && (<Icon icon="codicon:check" className=" text-icon-foreground absolute right-[12px] top-1/2 -translate-y-1/2 " width="14" height="14" />)}
                </div>
              );
            })}
          </div>
        </CustomScrollBar>
      </PopoverContent>
    </Popover>
  );
};
