const FileIconMap = {
  graphql: "vscode-icons:file-type-graphql",
  gql: "vscode-icons:file-type-graphql",
  babel: "vscode-icons:file-type-babel2",
  js: "vscode-icons:file-type-js",
  mjs: "vscode-icons:file-type-js",
  html: "vscode-icons:file-type-html",
  css: "vscode-icons:file-type-css",
  c: "vscode-icons:file-type-c",
  cpp: "vscode-icons:file-type-cpp",
  cs: "vscode-icons:file-type-csharp",
  go: "vscode-icons:file-type-go",
  editorconfig: "vscode-icons:file-type-editorconfig",
  png: "vscode-icons:file-type-image",
  jpg: "vscode-icons:file-type-image",
  jpeg: "vscode-icons:file-type-image",
  gif: "vscode-icons:file-type-image",
  webp: "vscode-icons:file-type-image",
  java: "vscode-icons:file-type-java",
  less: "vscode-icons:file-type-less",
  ini: "vscode-icons:file-type-light-ini",
  conf: "vscode-icons:file-type-light-ini",
  json: "vscode-icons:file-type-light-json",
  rs: "vscode-icons:file-type-rust",
  log: "vscode-icons:file-type-log",
  md: "vscode-icons:file-type-markdown",
  pdf: "vscode-icons:file-type-pdf2",
  php: "vscode-icons:file-type-php",
  py: "vscode-icons:file-type-python",
  jsx: "vscode-icons:file-type-reactjs",
  tsx: "vscode-icons:file-type-reactjs",
  proto: "vscode-icons:file-type-protobuf",
  r: "vscode-icons:file-type-r",
  sass: "vscode-icons:file-type-scss",
  scss: "vscode-icons:file-type-scss",
  svg: "vscode-icons:file-type-svg",
  stylus: "vscode-icons:file-type-stylus",
  tex: "vscode-icons:file-type-tex",
  txt: "vscode-icons:file-type-text",
  toml: "vscode-icons:file-type-toml",
  ts: "vscode-icons:file-type-typescript",
  vue: "vscode-icons:file-type-vue",
  wasm: "vscode-icons:file-type-wasm",
  yaml: "vscode-icons:file-type-yaml",
  yml: "vscode-icons:file-type-yaml",
  zip: "vscode-icons:file-type-zip",
  rar: "vscode-icons:file-type-zip",
  tar: "vscode-icons:file-type-zip",
  xml: "vscode-icons:file-type-xml",
  sh: "vscode-icons:file-type-shell",
  bash: "vscode-icons:file-type-shell",
} as const;

const DirIconMap = {
  "src": "vscode-icons:folder-type-src",
  "api": "vscode-icons:folder-type-api",
  "apis": "vscode-icons:folder-type-api",
  "app": "vscode-icons:folder-type-app",
  "apps": "vscode-icons:folder-type-app",
  "asset": "vscode-icons:folder-type-asset",
  "assets": "vscode-icons:folder-type-asset",
  "css": "vscode-icons:folder-type-css",
  "component": "vscode-icons:folder-type-component",
  "components": "vscode-icons:folder-type-component",
  "config": "vscode-icons:folder-type-config",
  "configs": "vscode-icons:folder-type-config",
  "db": "vscode-icons:folder-type-db",
  "dist": "vscode-icons:folder-type-dist",
  "dists": "vscode-icons:folder-type-dist",
  "doc": "vscode-icons:folder-type-docs",
  "docs": "vscode-icons:folder-type-docs",
  "font": "vscode-icons:folder-type-fonts",
  "fonts": "vscode-icons:folder-type-fonts",
  "graphql": "vscode-icons:folder-type-graphql",
  "helper": "vscode-icons:folder-type-helper",
  "helpers": "vscode-icons:folder-type-helper",
  "img": "vscode-icons:folder-type-images",
  "imgs": "vscode-icons:folder-type-images",
  "image": "vscode-icons:folder-type-images",
  "images": "vscode-icons:folder-type-images",
  "js": "vscode-icons:folder-type-js",
  "javascript": "vscode-icons:folder-type-js",
  "json": "vscode-icons:folder-type-json",
  "less": "vscode-icons:folder-type-less",
  "scss": "vscode-icons:folder-type-sass",
  "sass": "vscode-icons:folder-type-sass",
  "log": "vscode-icons:folder-type-log",
  "logs": "vscode-icons:folder-type-log",
  "middleware": "vscode-icons:folder-type-middleware",
  "middlewares": "vscode-icons:folder-type-middleware",
  "mock": "vscode-icons:folder-type-mock",
  "mocks": "vscode-icons:folder-type-mock",
  "next": "vscode-icons:folder-type-next",
  "nuxt": "vscode-icons:folder-type-nuxt",
  "package": "vscode-icons:folder-type-package",
  "packages": "vscode-icons:folder-type-package",
  "prisma": "vscode-icons:folder-type-prisma",
  "plugin": "vscode-icons:folder-type-plugin",
  "plugins": "vscode-icons:folder-type-plugin",
  "public": "vscode-icons:folder-type-public",
  "private": "vscode-icons:folder-type-private",
  "redux": "vscode-icons:folder-type-redux",
  "route": "vscode-icons:folder-type-route",
  "routes": "vscode-icons:folder-type-route",
  "router": "vscode-icons:folder-type-route",
  "routers": "vscode-icons:folder-type-route",
  "server": "vscode-icons:folder-type-server",
  "script": "vscode-icons:folder-type-script",
  "scripts": "vscode-icons:folder-type-script",
  "style": "vscode-icons:folder-type-style",
  "styles": "vscode-icons:folder-type-style",
  "temp": "vscode-icons:folder-type-temp",
  "tmp": "vscode-icons:folder-type-temp",
  "util": "vscode-icons:folder-type-tools",
  "utils": "vscode-icons:folder-type-tools",
  "tool": "vscode-icons:folder-type-tools",
  "tools": "vscode-icons:folder-type-tools",
  "video": "vscode-icons:folder-type-video",
  "videos": "vscode-icons:folder-type-video",
  "view": "vscode-icons:folder-type-view",
  "views": "vscode-icons:folder-type-view",
  "theme": "vscode-icons:folder-type-theme",
  "themes": "vscode-icons:folder-type-theme",
  "template": "vscode-icons:folder-type-template",
  "templates": "vscode-icons:folder-type-template",
  "test": "vscode-icons:folder-type-test",
  "tests": "vscode-icons:folder-type-test",
  ".vscode": "vscode-icons:folder-type-vscode",
};

const CommonConfigExt = [
  "js",
  "cjs",
  "json",
  "json5",
  "yaml",
  "yml",
  "toml",
];

// vscode-icons:file-type-karma
// vscode-icons:file-type-light-next
// vscode-icons:file-type-light-pnpm
// vscode-icons:file-type-light-prisma
// vscode-icons:file-type-mocha
// vscode-icons:file-type-nestjs
// vscode-icons:file-type-angular
// vscode-icons:file-type-node
// vscode-icons:file-type-yarn

const parseFilename = (filename: string) => {
  const idx = filename.lastIndexOf(".");
  let ext = "";
  let name = filename;
  if (idx > 0) {
    name = filename.slice(0, idx);
    ext = filename.slice(idx + 1);
  }

  return { name, ext };
};

const getDirIcon = (filename: string) => {
  const dirIcon = DirIconMap[filename as keyof typeof DirIconMap];
  if (dirIcon) {
    return dirIcon;
  }

  // 普通目录
  return "codicon:folder";
};

const getFileIcon = (filename: string) => {
  const getIconIfToolConfig = (name: string, ext: string) => {
    if (!["js", "cjs", "ts"].includes(ext)) {
      return null;
    }

    const regexp = /\.config$/;
    if (!regexp.test(name)) {
      return null;
    }

    const toolName = name.replace(regexp, "");
    if (["rollup", "vite", "webpack", "vue", "nuxt", "next"].includes(toolName)) {
      return `vscode-icons:file-type-${toolName}`;
    }

    return null;
  };

  const { ext, name } = parseFilename(filename);
  if (name === ".gitattributes" || name === ".gitignore") {
    return "vscode-icons:file-type-git";
  }
  if (ext === "swift") {
    return "vscode-icons:file-type-swift";
  }

  if (name === ".env") {
    return "vscode-icons:file-type-light-ini";
  }

  if (filename === ".npmrc" || filename === "package.json" || filename === "package-lock.json") {
    return "vscode-icons:file-type-npm";
  }

  if (filename === ".yarnrc" || filename === "yarn.lock") {
    return "vscode-icons:file-type-yarn";
  }

  if (
    name === ".eslintignore"
    || (name === ".eslintrc" && CommonConfigExt.includes(ext))
  ) {
    return "vscode-icons:file-type-eslint";
  }

  if ("dockerfile" === name.toLowerCase()) {
    return "vscode-icons:file-type-docker";
  }

  if (name.toLowerCase() === "gulpfile" && ["js", "cjs", "ts"].includes(ext)) {
    return "vscode-icons:file-type-gulp";
  }

  if (name.toLowerCase() === "gruntfile" && ["js", "cjs", "ts"].includes(ext)) {
    return "vscode-icons:file-type-grunt";
  }

  if (
    filename === ".prettierrc"
    || (name.toLowerCase() === ".prettierrc" && CommonConfigExt.includes(ext))
  ) {
    return "vscode-icons:file-type-prettier";
  }

  if (filename === "nginx.conf") {
    return "vscode-icons:file-type-nginx";
  }

  if (name === "postcss.config" && ["js", "cjs", "ts", "json"].includes(ext)) {
    return "vscode-icons:file-type-postcss";
  }

  if (name.split(".").includes("tsconfig") && ext === "json") {
    return "vscode-icons:file-type-tsconfig";
  }

  if (/\.d\.ts$/.test(filename)) {
    return "vscode-icons:file-type-typescriptdef";
  }

  let fileIcon = getIconIfToolConfig(name, ext);
  if (fileIcon) {
    return fileIcon;
  }

  // 基于扩展名匹配
  fileIcon = FileIconMap[ext as keyof typeof FileIconMap];
  if (fileIcon) {
    return fileIcon;
  }

  // 空文件
  return "ant-design:file-outlined";
};

export const getIcon = (filename: string, isDir: boolean) => isDir
  ? getDirIcon(filename)
  : getFileIcon(filename);

export function getFileExtension(language: string) {
  const extensions: Record<string, Array<string> | string> = {
    "python": ".py",
    "java": ".java",
    "c++": [".cpp", ".cc", ".cxx", ".hpp"],
    "c": [".c", ".h"],
    "javascript": [".js", ".jsx"],
    "typescript": [".ts", ".tsx"],
    "php": ".php",
    "ruby": ".rb",
    "swift": ".swift",
    "go": ".go",
    "rust": ".rs",
    "kotlin": ".kt",
    "r": ".r",
    "matlab": ".m",
    "shell": [".sh", ".bash"],
    "sql": ".sql",
    "html": ".html",
    "css": ".css",
    "xml": ".xml",
    "scala": ".scala",
    "perl": [".pl", ".pm"],
    "lua": ".lua",
    "dart": ".dart",
    "julia": ".jl",
    "vue": ".vue",
    "bash": ".sh",
    "sh": ".sh",
    "zsh": ".sh",
    "cmd": ".sh",
  };

  const res = extensions[language.toLowerCase()] ?? ".txt";

  if (typeof res === "string") {
    return res;
  }

  return res[0];
}
