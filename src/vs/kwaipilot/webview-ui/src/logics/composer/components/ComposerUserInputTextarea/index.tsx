import { UserInputTextareaProps, UserInputTextarea } from "@/logics/UserInputTextarea/UserInputTextArea";
import { UploadImageBtn } from "./UploadImageBtn";
import { useAgentPreferenceConfig } from "@/hooks/useAgentPreferenceConfig";

export const ComposerUserInputTextarea: React.FC<Omit<UserInputTextareaProps, "placeholder" | "mode">> = (props) => {
  const allowImage = useAgentPreferenceConfig();

  return (
    <UserInputTextarea
      placeholder={(
        <div className="absolute text-[13px] leading-[19.5px] text-tab-inactiveForeground top-2 left-3">
          有问题尽管问我，# 引用知识
        </div>
      )}
      action={allowImage
        ? (
            <UploadImageBtn />
          )
        : undefined}
      mode="composer"
      allowImage={allowImage}
      {...props}
    >
    </UserInputTextarea>
  );
};
