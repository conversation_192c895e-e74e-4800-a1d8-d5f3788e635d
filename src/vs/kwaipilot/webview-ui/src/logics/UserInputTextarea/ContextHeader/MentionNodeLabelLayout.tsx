import { Popover, Portal } from "@/components/Union/chakra-ui";
import { Box, Flex, PopoverContent, PopoverTrigger, useMergeRefs } from "@chakra-ui/react";
import { useCallback, useLayoutEffect, useMemo, useRef, useState } from "react";
import useResizeObserver from "use-resize-observer";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { clsx } from "clsx";
import { vsCss } from "@/style/vscode";

/**
 * 用于展示已添加的 知识 列表，超出两行折叠
 */
export function MentionNodeLabelLayout<T>({
  list,
  before,
  renderItem,
  renderRestItem,
}: {
  list: T[];
  before: React.ReactNode;
  renderItem: (item: T) => React.ReactNode;
  renderRestItem: (item: T) => React.ReactNode;
}) {
  /* lastVisibleFlag: 最后一个可见的 index,或-1 表示全部可见 */
  const [lastVisibleFlag, setLastVisibleFlag] = useState(-1);
  const containerRef = useRef<HTMLDivElement>(null);
  const measureItemRefs = useRef<Array<HTMLElement | null>>([]);

  const visibleNum = useMemo(() => lastVisibleFlag === -1 ? list.length : lastVisibleFlag + 1, [lastVisibleFlag, list.length]);

  const updateLastVisibleItemIndex = useCallback(() => {
    // 重置refs数组大小以匹配当前列表长度
    measureItemRefs.current = measureItemRefs.current.slice(0, list.length);

    if (!containerRef.current) return;

    // 获取容器的边界
    const containerRect = containerRef.current.getBoundingClientRect();
    const maxBottom = containerRect.top + 48; // maxHeight是48px

    let lastVisible = -1;

    // 检查每个项目是否在可见区域内
    for (let i = 0; i < measureItemRefs.current.length; i++) {
      const itemRef = measureItemRefs.current[i];
      if (!itemRef) continue;

      const itemRect = itemRef.getBoundingClientRect();

      // 如果元素底部在容器的可见范围内，则标记为可见
      if (itemRect.bottom <= maxBottom) {
        lastVisible = i;
      }
    }

    if (lastVisible > 0 && lastVisible < list.length - 1) {
      const itemRect = measureItemRefs.current[lastVisible]!.getBoundingClientRect();
      const isLastRow = itemRect.bottom > containerRect.bottom - itemRect.height && itemRect.top < containerRect.bottom;
      /* 最后一行 且容不下+N */
      if (isLastRow && itemRect.right + 48 > containerRect.right) {
        lastVisible -= 1;
      }
    }

    setLastVisibleFlag(lastVisible);
  }, [list]);

  useLayoutEffect(() => {
    updateLastVisibleItemIndex();
  }, [list, updateLastVisibleItemIndex]);

  const { ref: observedRef } = useResizeObserver({
    onResize() {
      updateLastVisibleItemIndex();
    },
  });

  const finalContainerRef = useMergeRefs(containerRef, observedRef);

  const invisibleItemCount = lastVisibleFlag === -1 ? 0 : list.length - lastVisibleFlag - 1;

  return (
    <Flex
      ref={finalContainerRef}
      alignItems="center"
      gap={1}
      wrap="wrap"
      maxHeight="48px"
      overflow="hidden"
      position="relative"
    >
      <Flex
        alignItems="center"
        gap={1}
        wrap="wrap"
        position="absolute"
        top="0"
        left={0}
        w="full"
        opacity={0}
        zIndex={-1}
      >
        {before}
        {list.map((v, index) => (
          <div
            key={`mention-item-${index}`}
            ref={el => measureItemRefs.current[index] = el}
            className={clsx(" inline-flex gap-1 items-center")}
          >
            {renderItem(v)}
          </div>
        ))}
      </Flex>
      {before}
      {list.slice(0, visibleNum).map((v, index) => (
        <div
          key={`mention-item-${index}`}
          className={clsx(" inline-flex gap-1 items-center")}
        >
          {renderItem(v)}
          {visibleNum < list.length && index === visibleNum - 1 && (
            <Popover placement="top-end">
              <PopoverTrigger>
                <Box
                  userSelect="none"
                  alignItems="center"
                  display="inline-flex"
                  gap={1}
                  as="button"
                  height="22px"
                  className="  cursor-pointer px-[6px] border border-solid  rounded hover:bg-toolbar-hoverBackground "
                >
                  <span className=" text-[12px]  text-foreground">
                    +
                    {invisibleItemCount}
                  </span>
                </Box>
              </PopoverTrigger>
              <Portal>

                <PopoverContent w="180px" py="2px" bg={vsCss.dropdownBackground}>
                  <CustomScrollBar className=" p-[2px] max-h-[240px]">
                    {list.slice(index + 1).map(v => renderRestItem(v))}
                  </CustomScrollBar>
                </PopoverContent>
              </Portal>
            </Popover>

          )}
        </div>
      ))}

    </Flex>
  );
}
