import { Box, BoxProps } from "@chakra-ui/react";
import { LexicalEditor } from "lexical";
import { useEffect, useMemo, useCallback } from "react";
import { ContextHeaderItem, mentionNodeUniqueKey, useContextHeaderContext } from "./ContextHeaderContext";
import { MentionEditButton } from "./MentionEditButton";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { useLatest } from "react-use";
import { MentionNodeLabelLayout } from "./MentionNodeLabelLayout";
import { MentionNodeLabel, MentionNodeLabel2, MentionNodeLabelReadonly, MentionNodeLabelReadonly2 } from "./MentionNodeLabel";
import { updateContextNodesByEditor } from "./updateContextNodesByEditor";

export function ContextHeader({
  editor,
  mode,
  allowImage,
  ...rest
}: {
  editor: LexicalEditor;
  mode: "chat" | "composer";
  allowImage?: boolean;
} & BoxProps) {
  const { state: { nodes, setNodes, markAsModified } } = useContextHeaderContext();
  const followActiveEditorNode = useMemo(() => {
    return nodes.find(node => node.followActiveEditor && !node.isVirtualContext);
  }, [nodes]);
  const restNodes = useMemo(() => {
    return nodes.filter(node => node !== followActiveEditorNode);
  }, [followActiveEditorNode, nodes]);

  const nodesRef = useLatest(nodes);

  const updateContextNodes = useCallback(() => {
    const frameId = requestAnimationFrame(() => {
      updateContextNodesByEditor({
        nodes: nodesRef.current,
        setNodes,
        markAsModified,
        editor,
      });
    });

    return () => {
      cancelAnimationFrame(frameId);
    };
  }, [editor, nodesRef, setNodes, markAsModified]);

  useEffect(() => {
    const cancel = updateContextNodes();
    return () => {
      cancel?.();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    return editor.registerTextContentListener(() => {
      updateContextNodes();
    });
  }, [editor, updateContextNodes]);

  const getUnavailableNodeInfo = useCallback((node: ContextHeaderItem) => {
    if (node.structure.type === "remoteImage" && !allowImage && mode === "composer") {
      return { reason: "" };
    }
    else {
      return undefined;
    }
  }, [allowImage, mode]);

  return (
    <Box position="relative" p={3} pb="6px" {...rest}>
      <MentionNodeLabelLayout
        before={(
          <>
            <MentionEditButton editor={editor} mode={mode} />
            {followActiveEditorNode && (
              <MentionNodeLabel
                onDelete={() => {
                  markAsModified();
                }}
                node={followActiveEditorNode}
                editor={editor}
                unavailable={getUnavailableNodeInfo(followActiveEditorNode)}
              />
            )}
          </>
        )}
        list={restNodes}
        renderItem={node => (
          <MentionNodeLabel
            key={mentionNodeUniqueKey(node.structure)}
            node={node}
            editor={editor}
            unavailable={getUnavailableNodeInfo(node)}
          />
        )}
        renderRestItem={node => (
          <MentionNodeLabel2
            key={mentionNodeUniqueKey(node.structure)}
            node={node}
            editor={editor}
            unavailable={getUnavailableNodeInfo(node)}
          />
        )}
      />
    </Box>
  );
}

export function ContextHeaderReadonly({
  persistentNodes,
  ...rest
}: {
  persistentNodes: MentionNodeV2Structure[];
} & BoxProps) {
  return (
    <Box position="relative" {...rest}>
      <MentionNodeLabelLayout<MentionNodeV2Structure>
        before={null}
        list={persistentNodes}
        renderItem={item => (
          <MentionNodeLabelReadonly key={mentionNodeUniqueKey(item)} structure={item} />
        )}
        renderRestItem={item => (
          <MentionNodeLabelReadonly2 key={mentionNodeUniqueKey(item)} structure={item} />
        )}
      />
    </Box>
  );
}
