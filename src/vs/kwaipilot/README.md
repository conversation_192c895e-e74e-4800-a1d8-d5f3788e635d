# Kwaipilot

![Kwaipilot](https://bs3-hb1.corp.kuaishou.com/upload-kinsight/kwaipilot/readme_pic/introPic/Kwaipilot-Intro.png)

> Kwaipilot，赋能研测工作，一款最懂快手研发的AI工具

## 简介

Kwaipilot是由研发管理办公室开发的人工智能代码助手，基于自研代码生成大模型，学习域内优质代码，可根据上下文和任务要求生成更符合实际研发需求的优质代码，通过与开发环境集成，将高效集成到您工作流程的每个部分。

Kwaipilot不仅是一个工具，更是您编程路上的得力助手。现在使用，体验编程的革命性变化！

## 主要特性
- 更懂快手代码的代码大模型
    - 基于程序分析的代码检索引擎，掌握快手高质量历史数据，助力全栈代码续写，提升编码效率，释放您的大脑生产力
    - 研发工作流更智能、更顺畅，支持生成代码注释、单元测试
    - 充分的工程优化，稳定保证秒级代码续写建议
    - 800+日活、1000+周活、70%+留存，支撑主站、电商、商业化、本地生活、海外等多个BU的代码生成需求，展现强大的产品力
- 智能问答
    - 快手私域大模型，检索公司技术与知识，更懂您的问题，更懂快手人
	- 最高 20万字 超长上下文，打开大模型应用新世界
	- 支持工具调用，如公域信息检索与理解
- 便捷易用，集成进入开发场景
    - 与 JetBrains IDE 和 VS Code 无缝集成，自动适应用户编码习惯，coding 从未如此丝滑

## 设置
> 首选项 -> 设置 -> 扩展 -> Kwaipilot
- 设置代理URI
- 设置代码补全等待时间
- 设置代码续写最大token数（自定义代码补全输出最大长度）
- 设置是否启用代码续写（默认启用）
- 设置是否启用注释续写（默认不启用）

## 使用
#### 1. 代码续写
![Code Completion](https://bs3-hb1.corp.kuaishou.com/upload-kinsight/kwaipilot/readme_pic/introPic/VSCode-Code-Completion.gif)
#### 2. 智能问答
![Intelligent QA](https://bs3-hb1.corp.kuaishou.com/upload-kinsight/kwaipilot/readme_pic/introPic/Intelligent-QA.gif)

---

### 其他
![Keyboard](https://bs3-hb1.corp.kuaishou.com/upload-kinsight/kwaipilot/readme_pic/introPic/Keyboard.png)

更多使用方法请查看：[Kwaipilot使用文档](https://docs.corp.kuaishou.com/k/home/<USER>/fcAAvma1OGznlywMIiYfgHz6w)

了解一下：[Kwaipilot](https://kinsight.corp.kuaishou.com/web/kwaipilot/home)

反馈一下：[啄木鸟平台](https://feedback.corp.kuaishou.com/#/home?businessLineId=3&nameEng=kinsight)