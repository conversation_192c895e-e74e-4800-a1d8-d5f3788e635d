import { useCallback, useEffect, useState } from "react";
import "./global.css";
import { FileIndex } from "./pages/fileIndex";
import { MCP } from "./pages/mcp";
import { Rules } from "./pages/rules";
import { BasicSetting } from "./pages/basics";
import { FunctionSetting } from "./pages/function";
import { kwaiPilotBridgeAPI } from "./bridge";
import { SettingPage } from "shared/lib/customSettingPanel/index";
import { Icon } from "@iconify/react";

declare global {
  interface Window {
    ide?: "vscode";
    vscMediaUrl: string;
    colorThemeName: "dark" | "light";
    webkit?: any;
    kwaipilotBridgeCall?: any;
    WKWebViewJavascriptBridge?: any;
    WKWVJBCallbacks?: any;
    bridge?: any;
    proxyUrl?: string;
    uriQuery?: string;
    __KWAIPILOT_ENV__IN_IDE__?: boolean;
  }
}

const pageMap: Record<SettingPage, () => JSX.Element> = {
  fileIndex: () => <FileIndex />,
  mcp: () => <MCP />,
  rules: () => <Rules />,
  basics: () => <BasicSetting />,
  function: () => <FunctionSetting />,
};

const App = () => {
  const [activeTab, setActiveTab] = useState<SettingPage>("basics");

  const handleTabClick = (tabId: SettingPage) => {
    setActiveTab(tabId);
  };

  const updateTheme = (theme: "dark" | "light") => {
    if (theme === "dark") {
      document.body.classList.add("dark");
    }
    else {
      document.body.classList.remove("dark");
    }
  };

  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI.currentTheme().subscribe((theme) => {
      updateTheme(theme);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI.customPanelPage().subscribe((value) => {
      setActiveTab(value);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, []);
  // const proxy = userSettingsStore(state => state);

  useEffect(() => {
    kwaiPilotBridgeAPI.extensionSettings.$getSettings().then((settings) => {
      import("./store/useSettingsStore").then(({ userSettingsStore }) => {
        // 获取所有设置并设置到store中
        const setters = userSettingsStore.getState();

        // 使用类型断言来解决索引访问问题
        const typedSettings = settings as Record<string, any>;

        // 遍历设置对象的所有键
        for (const key of Object.keys(typedSettings)) {
          const capitalizedKey = key.charAt(0).toUpperCase() + key.slice(1);
          const setterName = `set${capitalizedKey}`;
          const value = typedSettings[key];

          // 检查setter方法是否存在
          if (setterName in setters) {
            // 调用对应的setter方法更新值
            (setters as any)[setterName](value);
          }
        }
      });
    });
    kwaiPilotBridgeAPI.observableAPI.settingUpdate().subscribe(({ key, value }) => {
      import("./store/useSettingsStore").then(({ userSettingsStore }) => {
        // 获取所有设置并设置到store中
        const setters = userSettingsStore.getState();

        // 根据键名构造对应的setter方法名
        // 首先获取键的第一个字符并转为大写
        const capitalizedKey = key.charAt(0).toUpperCase() + key.slice(1);
        const setterName = `set${capitalizedKey}`;

        // 检查setter方法是否存在
        if (setterName in setters) {
          // 调用对应的setter方法更新值
          (setters as any)[setterName](value);
        }
        else {
          console.error(`Setter method ${setterName} not found for key ${key}`);
        }
      });
    });
  }, []);
  const reportPageShowEvent = useCallback(() => {
    switch (activeTab) {
      case "rules":
        kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction({
          key: "rules_page_show",
        });
        break;
      case "mcp":
        kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction({
          key: "mcp_page_show",
        });
        break;
      case "fileIndex":
        kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction({
          key: "file_index_page_show",
        });
        break;
      case "basics":
        kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction({
          key: "basics_page_show",
        });
        break;
      case "function":
        kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction({
          key: "function_page_show",
        });
        break;
    }
  }, [activeTab]);

  useEffect(() => {
    reportPageShowEvent();
  }, [activeTab, reportPageShowEvent]);

  // 渲染左侧标签头
  const renderTabHeaders = () => {
    return (
      <div className="flex flex-col w-full h-full overflow-y-auto gap-[8px]">
        {
          ([{
            label: "基础",
            value: "basics",
            icon: "codicon:gear",
          }, {
            label: "功能",
            value: "function",
            icon: "codicon:tools",
          }, {
            label: "代码索引",
            value: "fileIndex",
            icon: "codicon:gist",
          }, {
            label: "MCP",
            value: "mcp",
            icon: "codicon:server",
          }, {
            label: "规则配置",
            value: "rules",
            icon: "codicon:symbol-ruler",
          }] as Array<{
            label: string;
            value: SettingPage;
            icon: string;
          }>).map(item => (
            <div
              key={item.value}
              className={`h-[32px] px-[12px] cursor-pointer rounded-[4px] flex text-[var(--vscode-foreground)] items-center gap-[8px] ${
                activeTab === item.value
                  ? "bg-[var(--vscode-list-hoverBackground)]"
                  : ""
              }`}
              onClick={() => handleTabClick(item.value)}
            >
              <Icon icon={item.icon} className="size-[16px]" />
              {item.label}
            </div>
          ))
        }
      </div>
    );
  };

  return (
    <div className="h-[100vh] w-[100vw] flex flex-col">
      <div className="flex flex-1 pt-[24px] pl-[24px] pr-[0] pb-[24px] gap-[24px] overflow-hidden">
        <div className="max-w-[200px] w-[30%] h-full min-w-[100px]">
          {renderTabHeaders()}
        </div>
        <div className="w-[1px] bg-[var(--vscode-widget-border)] flex-shrink-0"></div>
        <div className="flex-1 flex-shrink-1 h-full overflow-y-auto overflow-x-hidden">
          <div className="max-w-[900px] pr-[24px] min-w-[456px]">
            {pageMap[activeTab]()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
