import { menuAnatomy } from "@chakra-ui/anatomy";
import { createMultiStyleConfigHelpers } from "@chakra-ui/react";

const { definePartsStyle, defineMultiStyleConfig }
  = createMultiStyleConfigHelpers(menuAnatomy.keys);

export default defineMultiStyleConfig({
  baseStyle: definePartsStyle({
    button: {
      _expanded: {
        bg: "var(--vscode-button-secondaryBackground)",
      },
    },
    list: {
      bg: "var(--vscode-dropdown-background)",
      border: "1px solid",
      borderColor: "var(--vscode-dropdown-border)",
      borderRadius: "4px",
      padding: "4px",
      minWidth: "0",
      boxShadow: "none",
    },
    item: {
      bg: "transparent",
      color: "var(--vscode-foreground)",
      borderRadius: "2px",
      fontsize: "13px",
      lineHeight: "20px",
      padding: "6px 12px",
      _hover: {
        bg: "var(--vscode-list-hoverBackground)",
      },
    },
  }),
  defaultProps: { },
});
