import { defineStyleConfig } from "@chakra-ui/react";

// define the base component styles
const baseStyle = {
  borderRadius: "4px", // add a border radius
  fontWeight: "normal", // change the font weight
  backgroundColor: "var(--vscode-editor-background)",
  color: "var(--vscode-foreground)",
  fontSize: "14px",
  lineHeight: "22px",
  padding: "5px 8px",
  ["--popper-arrow-bg"]: "var(--vscode-editor-background)",
};

// export the component theme
export default defineStyleConfig({ baseStyle });
