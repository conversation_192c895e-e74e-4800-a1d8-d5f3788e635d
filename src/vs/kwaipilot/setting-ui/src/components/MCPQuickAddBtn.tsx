import { useState, useMemo } from "react";
import { useMcpStore } from "../store/useMcpStore";
import { Icon } from "@iconify/react";
import { useAsync } from "react-use";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { Button, Input, Popover, PopoverContent, PopoverTrigger, Tooltip } from "@chakra-ui/react";
import { MCP_MARKET_URL } from "@/const/mcp";

export const MCPQuickAddBtn = () => {
  const [visible, setVisible] = useState(false);
  const servers = useMcpStore(state => state.servers);
  const featuredServers = useMcpStore(state => state.featuredServers);
  const setFeaturedServers = useMcpStore(state => state.setFeaturedServers);
  const setQuickAddMcpServer = useMcpStore(state => state.setQuickAddMcpServer);
  const [inputValue, setInputValue] = useState("");

  useAsync(async () => {
    const res = await kwaiPilotBridgeAPI.extensionMCP.$getMcpFeaturedServers();
    setFeaturedServers(res?.data?.records ?? []);
  }, [setFeaturedServers]);

  const matchFeaturedServers = useMemo(() => {
    return featuredServers.filter(server => server?.serverName?.toLowerCase().includes(inputValue.toLowerCase()));
  }, [featuredServers, inputValue]);

  const renderItems = useMemo(() => {
    if (!matchFeaturedServers.length) {
      return <div className="text-center text-[var(--vscode-descriptionForeground)] py-2">暂无数据</div>;
    }
    return matchFeaturedServers.map((server, i) => {
      const name = server?.serverName;
      const index = Number(name?.toLowerCase().indexOf(inputValue.toLowerCase()));
      const beforeStr = name?.slice(0, index);
      const matchStr = name?.slice(index, index + inputValue.length);
      const afterStr = name?.slice(index + inputValue.length);

      const isInstalled = servers.some(server => server?.name === name);

      return (
        <div key={i} className="flex justify-between items-center hover:bg-[var(--vscode-list-hoverBackground)] h-[56px] p-[12px]">
          <div className="flex-1 mr-3">
            <div className="flex items-center mb-1">
              <div
                className="break-all line-clamp-1 text-[var(--vscode-settings-headerForeground)] text-[14px] font-medium mr-2"
              >
                {beforeStr}
                <span className="text-[var(var(--vscode-textLink-foreground))]">{matchStr}</span>
                {afterStr}
              </div>
              <div className="px-[4px] rounded text-[12px] bg-[var(--vscode-badge-background)] text-[var(--vscode-foreground)] mr-[6px] flex-shrink-0">精选</div>
              {server.internalProvide && <div className="text-[12px] text-[var(--vscode-foreground)] flex-shrink-0">· 快手</div>}
            </div>
            <Tooltip label={server.serverDescription} placement="top" hasArrow>
              <div className="break-all line-clamp-1 text-[var(--vscode-descriptionForeground)]">{server.serverDescription}</div>
            </Tooltip>
          </div>
          {
            isInstalled
              ? (
                  <Tooltip label="已添加" placement="top" hasArrow>
                    <div
                      className="flex-shrink-0 flex items-center justify-center size-6 bg-[var(--vscode-list-inactiveSelectionBackground)] rounded text-[var(--vscode-icon-foreground)]"
                    >
                      <Icon icon="ion:checkmark" className="size-[16px]" />
                    </div>
                  </Tooltip>
                )
              : (
                  <Tooltip label="添加" placement="top" hasArrow>
                    <div
                      className="flex-shrink-0 flex items-center justify-center size-6 cursor-pointer bg-[var(--vscode-list-dropBetweenBackground)] rounded text-[var(--vscode-editor-background)]"
                      onClick={() => {
                        setQuickAddMcpServer(server);
                        setVisible(false);
                        setInputValue("");
                      }}
                    >
                      <Icon icon="ion:add-outline" className="size-[16px]" />
                    </div>
                  </Tooltip>
                )
          }
        </div>
      );
    });
  }, [matchFeaturedServers, inputValue, setQuickAddMcpServer, servers]);

  return (
    <>
      <Popover
        placement="bottom-end"
        onClose={() => {
          setVisible(false);
          setInputValue("");
        }}
        isOpen={visible}
      >
        <PopoverTrigger>
          <Button onClick={() => setVisible(true)} variant="blueSolid">
            快捷配置
          </Button>
        </PopoverTrigger>
        <PopoverContent>
          <div className="!outline-none">
            <div className="p-[12px]">
              <Input
                value={inputValue}
                placeholder="请输入MCP server名称"
                onChange={e => setInputValue(e.target.value)}
              />
            </div>
            <div className="max-h-[475px] overflow-y-auto">
              {renderItems}
            </div>
            <div className="border-t border-[var(--vscode-widget-border)] text-[var(--vscode-descriptionForeground)] text-[12px] py-2 px-3">
              其他 MCP Server 可前往
              {" "}
              <a href={MCP_MARKET_URL} target="_blank" className="text-[var(--vscode-textLink-foreground)] hover:text-[var(--vscode-textLink-activeForeground)]">MCP市场</a>
              {" "}
              进行手动添加
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
};
