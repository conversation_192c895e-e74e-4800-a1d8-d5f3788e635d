export default {
  meta: {
    type: "problem",
    docs: {
      description: "强制使用 @bridge 、@/bridge 和 @webview 方式引入模块",
      category: "Best Practices",
      recommended: true,
    },
    fixable: "code",
    schema: [], // 不需要额外的配置选项
  },

  create(context) {
    const filePath = context.getFilename();
    // 检查是否在webview-ui目录下
    const isInWebviewUI = filePath.includes("/webview-ui/src/");

    return {
      ImportDeclaration(node) {
        const importPath = node.source.value;

        // 豁免规则1: src/vs/kwaipilot/src目录下对shared/types/bridge相关的导入豁免
        if (importPath.includes("shared/types/bridge")) {
          return; // 跳过检查
        }

        // 豁免规则2: webview-ui目录下对bridge-export目录的导入豁免
        if (importPath.includes("bridge-export") || importPath.includes("bridge-share")) {
          return; // 跳过检查
        }

        // 检查是否包含 bridge 或 webview 关键字
        if (importPath.includes("bridge") || importPath.includes("webview")) {
          // 检查是否使用了相对路径
          if (importPath.startsWith(".")) {
            // 根据文件路径应用不同的规则
            if (isInWebviewUI && importPath.includes("bridge")) {
              context.report({
                node,
                message: "在webview-ui中必须使用 @/bridge 方式引入模块，不允许使用相对路径",
                fix(fixer) {
                  // 获取原始导入路径
                  const originalPath = node.source.value;

                  // 处理../bridge这样的导入
                  if (originalPath === "../bridge" || originalPath.endsWith("/bridge")) {
                    return fixer.replaceText(node.source, `"@/bridge"`);
                  }

                  // 处理../bridge/xxx这样的导入
                  if (originalPath.includes("/bridge/")) {
                    const parts = originalPath.split("/bridge/");
                    const moduleName = parts[parts.length - 1];
                    return fixer.replaceText(node.source, `"@/bridge/${moduleName}"`);
                  }

                  return null;
                },
              });
            }
            else if (!isInWebviewUI && (importPath.endsWith("bridge") || importPath.endsWith("webview"))) {
              // 针对src/vs/kwaipilot/src目录下的文件
              context.report({
                node,
                message: "必须使用 @bridge 或 @webview 方式引入模块，不允许使用相对路径",
                fix(fixer) {
                  // 获取原始导入路径
                  const originalPath = node.source.value;

                  // 确定应该使用的别名
                  const alias = originalPath.includes("bridge") ? "@bridge" : "@webview";

                  // 提取模块的最后一部分作为新路径
                  const parts = originalPath.split("/");
                  const moduleName = parts[parts.length - 1];

                  // 构建新的导入路径
                  const newPath = `${alias}/${moduleName}`;

                  // 修复导入语句
                  return fixer.replaceText(node.source, `"${newPath}"`);
                },
              });
            }
          }
        }
      },
    };
  },
};
