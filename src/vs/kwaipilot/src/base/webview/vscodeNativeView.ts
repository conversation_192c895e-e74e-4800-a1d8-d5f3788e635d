import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import * as vscode from "vscode";

import { StateManager } from "../../common/state";
import { LoggerManager } from "../logger";
import { Bridge } from "@bridge";
import { ReportKeys, ReportOpt } from "shared/lib/misc/logger";
import { WebloggerManager } from "../weblogger";
import { createExtensionRpcContext } from "../bridge/ExtensionRpcContext";
import { NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME } from "shared/lib/bridge";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";

export class Webview extends BaseModule {
  _view: vscode.WebviewView;
  rpcContext: IRPCProtocol;
  private readonly _onDidChangeVisibility = new vscode.EventEmitter<void>();
  // todo 暂时先mock，后续看业务逻辑调整为有效 onDidChangeVisibility
  public readonly onDidChangeVisibility = this._onDidChangeVisibility.event;
  // 默认值设置为 true
  private _visible = true;
  // 用于存储监听命令的订阅，以便在销毁时取消订阅
  private _auxiliaryBarCommandDisposable?: vscode.Disposable;
  private readonly loggerScope = "webview";

  constructor(ext: ContextManager) {
    super(ext);
    this._view = {
      webview: this,
      visible: this._visible,
      onDidChangeVisibility: this.onDidChangeVisibility,
    } as unknown as vscode.WebviewView;
    StateManager.initInstance(this.context);
    this.rpcContext = createExtensionRpcContext({
      logger: () => this.getBase(LoggerManager),
      protocol: {
        send: (message) => {
          this.getBase(Bridge).postOneWayMessage(this as unknown as vscode.Webview, WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE, message);
        },
        onMessage: (listener) => {
          return this.getBase(Bridge).registerOneWayMessageHandler(NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE, (data, source) => {
            console.log("kwaipilot onMessage", data);
            if (source !== this._view?.webview) {
              return;
            }
            listener(data);
          });
        },
      },
    });

    vscode.commands.registerCommand(
      "kwaipilot.bridge.postMessageFromUI",
      (message) => {
        if (message.protocol === "callHandler") {
          this.getBase(Bridge).callNativeHandler(
            this as unknown as vscode.Webview,
            message.name,
            message.data,
            message.callbackId,
          );
        }
        else if (message.protocol === "callback") {
          this.getBase(Bridge).handleCallback(message.callbackId, message.data);
        }
        else if (message.protocol === "message") {
          this.getBase(Bridge).handleOneWayMessage(this as unknown as vscode.Webview, message.data);
        }
      },
    );

    // 监听 AIAssistant 辅助侧边栏可见性变化命令
    this._setupAuxiliaryBarVisibilityListener(ext.context);
  }

  /**
   * 设置辅助侧边栏可见性监听器
   * 使用 command 监听 aiAssistant.auxiliaryBarVisibilityChanged 命令
   */
  private _setupAuxiliaryBarVisibilityListener(context: vscode.ExtensionContext): void {
    this.logger.info(`初始化辅助侧边栏状态: ${this._visible ? "可见" : "隐藏"}`, this.loggerScope);

    // 注册命令监听器，监听 AIAssistant 发出的辅助侧边栏可见性变化通知
    this._auxiliaryBarCommandDisposable = vscode.commands.registerCommand(
      "aiAssistant.auxiliaryBarVisibilityChanged",
      (params: { isVisible: boolean; timestamp: number }) => {
        this.visible = params.isVisible;
        this.logger.info(
          `收到辅助侧边栏状态变化通知: ${params.isVisible ? "可见" : "隐藏"}, 时间戳: ${params.timestamp}`,
          this.loggerScope,
        );
      },
    );

    // 添加到生命周期管理
    if (this._auxiliaryBarCommandDisposable) {
      context.subscriptions.push(this._auxiliaryBarCommandDisposable);
    }
  }

  /**
   * 显示并聚焦 webivew 页面
   * @returns Promise
   */
  public focus(form: ReportKeys["sidebar_show"]) {
    const param: ReportOpt<"sidebar_show"> = {
      key: "sidebar_show",
      type: form,
    };
    this.getBase(WebloggerManager)?.$reportUserAction(param);
    vscode.commands.executeCommand("kwaiPilotChatWebView.focus");
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  public postMessage(message: any) {
    const commandId = "kwaipilot.bridge.postMessageFromExtension";

    vscode.commands.executeCommand(commandId, message);
  }

  public get visible(): boolean {
    return this._visible;
  }

  public set visible(value: boolean) {
    if (this._visible !== value) {
      this._visible = value;
      this._onDidChangeVisibility.fire();
    }
  }

  // 确保资源能够正确释放
  public dispose(): void {
    if (this._auxiliaryBarCommandDisposable) {
      this._auxiliaryBarCommandDisposable.dispose();
      this._auxiliaryBarCommandDisposable = undefined;
    }
  }
}
