import axios, { CanceledError, Canceler } from "axios";
import Stream from "stream";
import { LoggerManager } from "../../base/logger";

export interface CompletionResult {
  code: string;
  completionLogprod?: number;
  modelType?: string;
  modelVersion?: string;
  reason?: string;
}

export interface BackgroundCollectionOptions {
  onFirstLine?: (result: CompletionResult) => void;
  onComplete?: (result: CompletionResult | null) => void;
  onError?: (error: any) => void;
  onFreshLine?: (result: CompletionResult) => void;
}

/**
 * 后台收集器类
 * 负责在后台收集续写结果，支持早期返回和取消操作
 */
export class BackgroundCollector {
  private readonly loggerScope = "debugger-BackgroundCollector";
  private cancelToken: Canceler | null = null;
  private isCollecting = false;
  private collectedResult: CompletionResult | null = null;
  private freshData: string = "";
  private firstLineReturned = false;
  // 0620的续写方案 https://docs.corp.kuaishou.com/k/home/<USER>/fcADKYakcUIvB66873bcuutSF
  is0620: boolean = false;

  constructor(private logger: LoggerManager) {}
  private reset() {
    this.cancelToken = null;
    this.isCollecting = false;
    this.collectedResult = null;
    this.freshData = "";
    this.firstLineReturned = false;
    this.is0620 = false;
  }

  /**
   * 开始后台收集
   * @param url 请求URL
   * @param data 请求数据
   * @param options 回调选项
   * @returns Promise<CompletionResult | null> 第一行结果（早期返回）
   */
  async startCollection(
    url: string,
    data: any,
    options: BackgroundCollectionOptions = {},
  ): Promise<CompletionResult | null> {
    // 取消之前的请求
    this.cancel();
    this.reset();
    this.isCollecting = true;

    return new Promise((resolve, reject) => {
      const request = axios({
        method: "post",
        url,
        data,
        responseType: "stream",
        cancelToken: new axios.CancelToken((cancel) => {
          this.cancelToken = cancel;
        }),
      });

      request
        .then((response) => {
          const stream = response.data as Stream;

          stream.on("data", (chunk: string) => {
            const chunkStr = chunk.toString();
            if (chunkStr === "data:" || chunkStr === "\n\n") {
              return;
            }
            this.freshData = chunkStr.split("data:").join("").trim();
            this.is0620 = this.tryParseCompletionShowStrategy(this.freshData);
            if (!this.is0620) {
              this.logger.info("不是灰度用户，不解析行", this.loggerScope, { value: { freshData: chunkStr } });
              return;
            }

            // 尝试解析第一行完整结果
            if (!this.firstLineReturned) {
              const firstLineResult = this.tryParseFirstLine(this.freshData);
              if (firstLineResult) {
                this.logger.info(`新续写策略，先返回第一行代码: ${firstLineResult.code}`, this.loggerScope);
                this.firstLineReturned = true;
                options.onFirstLine?.(firstLineResult);
                resolve(firstLineResult);
              }
            }
            else {
              // 更新最新的补全代码
              const freshResult = this.tryParseCodeWithFullLine(this.freshData);
              if (freshResult) {
                options.onFreshLine?.(freshResult);
              }
            }
          });

          stream.on("end", () => {
            this.isCollecting = false;
            this.logger.info("续写接口请求结束", this.loggerScope);

            // 解析最终完整结果
            const finalResult = this.parseFinalResult(this.freshData);
            this.collectedResult = finalResult;

            // 如果还没有返回第一行，现在返回完整结果
            if (finalResult) {
              if (!this.firstLineReturned) {
                this.logger.info(`新续写策略，但只有一行代码或旧的续写策略，直接返回: ${finalResult.code}`, this.loggerScope);
                resolve(finalResult);
              }
              else {
                // 只有多行续写，才需要resolve结果
                this.logger.info(`新策略最终补全代码: ${finalResult.code}`, this.loggerScope, { value: { finalResult } });
                options.onComplete?.(finalResult);
              }
            }
            else {
              this.logger.info("请求结束时没有返回结果", this.loggerScope);
              resolve(null);
            }
          });

          stream.on("error", (err) => {
            this.isCollecting = false;

            if (err instanceof CanceledError) {
              this.logger.info("取消请求", this.loggerScope);
              if (!this.firstLineReturned) {
                resolve(null);
              }
            }
            else {
              this.logger.error("续写接口报错", this.loggerScope, { err });
              options.onError?.(err);
              if (!this.firstLineReturned) {
                reject(err);
              }
            }
          });
        })
        .catch((err) => {
          this.isCollecting = false;

          if (err instanceof CanceledError) {
            this.logger.info("取消请求", this.loggerScope);
            if (!this.firstLineReturned) {
              resolve(null);
            }
          }
          else {
            this.logger.error("续写接口报错", this.loggerScope, { err });
            options.onError?.(err);
            if (!this.firstLineReturned) {
              reject(err);
            }
          }
        });
    });
  }

  /**
   * 尝试解析最新完整行数据
   */
  private tryParseCodeWithFullLine(data: string): CompletionResult | null {
    try {
      const jsonStr = data;
      if (!jsonStr) {
        return null;
      }
      const info = JSON.parse(jsonStr) as {
        code: string;
        completionLogprod: number;
        modelType: string;
        modelVersion: string;
      };
      if (!info.code.includes("\n")) {
        return null;
      }
      return {
        code: info.code.split("\n").slice(0, -1).join("\n"),
        completionLogprod: info.completionLogprod,
        modelType: info.modelType,
        modelVersion: info.modelVersion,
      };
    }
    catch (error) {
      return null;
    }
  }

  /**
   * 尝试判断是否新方案
   */

  private tryParseCompletionShowStrategy(data: string): boolean {
    return data.includes("completionShowStrategy");
  }

  /**
   * 尝试解析第一行完整结果
   * @param data 累积的数据
   * @returns 第一行结果或null
   */
  private tryParseFirstLine(data: string): CompletionResult | null {
    try {
      // 查找第一个完整的data块
      const jsonStr = data;
      if (!jsonStr) {
        return null;
      }
      const info = JSON.parse(jsonStr) as {
        code: string;
        completionLogprod: number;
        modelType: string;
        modelVersion: string;
      };
      if (!info.code.includes("\n")) {
        return null;
      }
      return {
        code: info.code.split("\n")[0],
        completionLogprod: info.completionLogprod,
        modelType: info.modelType,
        modelVersion: info.modelVersion,
      };
    }
    catch (error) {
      return null;
    }
  }

  /**
   * 解析最终完整结果
   * @param data 完整的数据
   * @returns 完整结果或null
   */
  private parseFinalResult(data: string): CompletionResult | null {
    try {
      // 查找第一个完整的data块
      const jsonStr = data;
      if (!jsonStr) {
        return null;
      }
      const info = JSON.parse(jsonStr) as {
        code: string;
        completionLogprod: number;
        modelType: string;
        modelVersion: string;
      };
      if (!info.code) {
        return null;
      }
      const res = {
        code: info.code,
        completionLogprod: info.completionLogprod,
        modelType: info.modelType,
        modelVersion: info.modelVersion,
      };
      if (!this.firstLineReturned) {
        // 只有一行
        this.logger.info("Background collection first line not returned, returning final result", this.loggerScope);
        return res;
      }
      return res;
    }
    catch (error) {
      this.logger.warn("Error parsing first line", this.loggerScope, { err: error });
      return null;
    }
  }

  /**
   * 取消当前收集
   */
  cancel(): void {
    if (this.cancelToken) {
      this.cancelToken("用户取消请求");
      this.cancelToken = null;
    }
    this.isCollecting = false;
  }

  /**
   * 获取收集状态
   */
  getCollectionStatus(): {
    isCollecting: boolean;
    hasResult: boolean;
    result: CompletionResult | null;
  } {
    return {
      isCollecting: this.isCollecting,
      hasResult: !!this.collectedResult,
      result: this.collectedResult,
    };
  }

  /**
   * 清理收集结果
   */
  cleanup(): void {
    this.cancel();
    this.collectedResult = null;
  }
}
