import * as vscode from "vscode";
import { WebloggerManager } from "../../base/weblogger";
import { Api } from "../../base/http-client";
import CommonDocument from "../../utils/recentDocument";
import { LoggerManager } from "../../base/logger";
import { buildDeviceLog, PLUGIN_PLATFORM, PLUGIN_VERSION } from "../../log/Model";
import { getRelativePath } from "../../utils";
import { getProjectInfo } from "../../utils/projectInfo";
import { ConfigManager, GlobalStateManager } from "../../base/state-manager";
import { Config, GlobalState } from "shared/lib/state-manager/types";
import { DocumentManager } from "../document";
import { BackgroundCollector, CompletionResult } from "./backgroundCollector";
import { v4 as uuidv4 } from "uuid";
import { createDeferred, Deferred } from "../../utils/deferred";
import { isNumber } from "lodash";

/**
 * 请求续写控制器
 *
 * 功能特性：
 * 1. 支持早期返回第一行结果
 * 2. 后台继续收集完整续写内容
 * 3. 支持取消操作
 * 4. 维护续写状态缓存
 */
export class RequestController {
  private readonly loggerScope = "debugger-RequestController";
  private device = buildDeviceLog();
  private backgroundCollector: BackgroundCollector;
  private currentCompletionId: string | null = null;
  private lastCompletionResult: CompletionResult | null = null;
  private codeCompletionOffset: number = 0;
  requestId: string = "";
  showIndex: number | undefined = undefined;

  private deferred: Deferred<string> | null = null;

  constructor(
    private logger: LoggerManager,
    private config: ConfigManager,
    private globalState: GlobalStateManager,
    private docManager: DocumentManager,
    private weblogger: WebloggerManager,
    private api: Api,
  ) {
    this.backgroundCollector = new BackgroundCollector(logger);
  }

  private resetCompletionState() {
    // 取消之前的请求
    this.cancelCurrentRequest();
    this.codeCompletionOffset = 0;
    this.showIndex = undefined;
    this.deferred = null;
  }

  private reportCoding = () => {
    const lastReportTime = this.globalState.get(GlobalState.LAST_REPORT_TIME);
    const currentTime = new Date();

    if (lastReportTime) {
      const lastReportDate = new Date(lastReportTime);
      const isSameDay = lastReportDate.toDateString() === currentTime.toDateString();

      if (!isSameDay) {
        // 上报用户使用情况的逻辑
        this.weblogger.$reportUserAction({ key: "coding" });

        // 更新上次上报时间
        this.globalState.update(GlobalState.LAST_REPORT_TIME, currentTime.toDateString());
      }
    }
    else {
      this.weblogger.$reportUserAction({ key: "coding" });
      this.globalState.update(GlobalState.LAST_REPORT_TIME, currentTime.toDateString());
    }
  };

  /**
   * 生成补全请求的唯一ID
   */
  private generateCompletionId(doc: vscode.TextDocument, pos: vscode.Position): string {
    const content = doc.getText(new vscode.Range(new vscode.Position(0, 0), pos));
    return `${doc.fileName}-${pos.line}-${pos.character}-${content.length}`;
  }

  /**
   * 请求代码续写 - 优化版本
   * 第一行内容生成后立即返回，后台继续收集完整结果
   * @param doc 文档对象
   * @param pos 光标位置
   * @returns 代码续写响应数组（第一行结果）
   */
  async requestCodeCompletion(doc: vscode.TextDocument, pos: vscode.Position) {
    this.reportCoding();
    const startTime = Date.now();
    const completionId = this.generateCompletionId(doc, pos);
    this.resetCompletionState();
    this.currentCompletionId = completionId;

    const url = this.config.get(Config.PROXY_URL);
    const requestData = this.getCompletionParams(doc, pos);

    try {
      const icon = "$(loading~spin)";
      const statusPromise = this.backgroundCollector.startCollection(
        `${url}/eapi/kwaipilot/code/completions/stream`,
        requestData,
        {
          onFirstLine: (result) => {
            this.logger.info("First line received", this.loggerScope, {
              value: { firstLineLength: result.code.length },
            });
            // 第一行到达时的处理
            const quickIcon = "$(notebook-state-success)";
            vscode.window.setStatusBarMessage(`${quickIcon} Kwaipilot: First line ready!`, 2000);
          },
          onFreshLine: (result) => {
            this.logger.info("Fresh line received", this.loggerScope, {
              value: { freshLineLength: result.code.length },
            });
            // 后台收集的处理
            this.lastCompletionResult = result;
            this.tryResolveDeferred({ sourceCode: result.code });
          },
          onComplete: (result) => {
            this.lastCompletionResult = result;
            this.currentCompletionId = null;
            if (result) {
              this.tryResolveDeferred({ sourceCode: result.code, isCompletion: true });
            }

            // 上报成功指标
            this.api.reportCost({
              beginTimestamp: startTime,
              namespace: "completions",
              stage: "completion",
              extra1: "count",
              extra2: result ? "success" : "empty",
            });

            const icon = "$(notebook-state-success)";
            vscode.window.setStatusBarMessage(`${icon} Kwaipilot: Finished!`, 3000);
            this.logger.info("Background collection completed", this.loggerScope, {
              value: { hasResult: !!result, completionId },
            });
          },
          onError: (error) => {
            this.currentCompletionId = null;
            this.tryRejectDeferred({ reason: String(error) });
            // 上报错误指标
            this.api.reportCost({
              beginTimestamp: startTime,
              namespace: "completions",
              stage: "completion",
              extra1: "count",
              extra2: "error",
            });

            this.logger.error("Background collection error", this.loggerScope, {
              err: error,
              value: { completionId },
            });
          },
        },
      );

      // 显示加载状态
      vscode.window.setStatusBarMessage(`${icon} Kwaipilot: Loading...`, statusPromise);

      // 等待第一行结果（早期返回）
      const firstLineResult = await statusPromise;

      if (!firstLineResult || firstLineResult.code.length === 0) {
        // 如果没有结果，上报并返回null
        this.api.reportCost({
          beginTimestamp: startTime,
          namespace: "completions",
          stage: "completion",
          extra1: "count",
          extra2: firstLineResult?.reason === "error" ? "error" : "success",
        });
        return null;
      }
      this.codeCompletionOffset += firstLineResult.code.length;
      if (this.backgroundCollector.is0620) {
        this.showIndex = 0;
      }
      this.logger.info(`第${this.showIndex}次续写结果曝光: ${firstLineResult.code}, 此时偏移量为${this.codeCompletionOffset}`, this.loggerScope);

      // 返回第一行结果，格式化为原有的响应格式
      return [{
        code: firstLineResult.code,
        completionLogprod: firstLineResult.completionLogprod,
        modelType: firstLineResult.modelType,
        modelVersion: firstLineResult.modelVersion,
      }];
    }
    catch (error) {
      this.currentCompletionId = null;
      // 上报错误指标
      this.api.reportCost({
        beginTimestamp: startTime,
        namespace: "completions",
        stage: "completion",
        extra1: "count",
        extra2: "error",
      });
      this.logger.error("Request code completion error", this.loggerScope, {
        err: error,
        value: { completionId },
      });
      throw error;
    }
  }

  /**
   * 取消当前请求
   */
  private cancelCurrentRequest(): void {
    if (this.currentCompletionId) {
      this.logger.info("取消当前后台进行的请求", this.loggerScope, {
        value: { completionId: this.currentCompletionId },
      });
      this.backgroundCollector.cancel();
      this.currentCompletionId = null;
    }
  }

  /**
   * 获取后台收集状态
   */
  getCollectionStatus() {
    return this.backgroundCollector.getCollectionStatus();
  }

  /**
   * 获取最后的完整续写结果
   * 用于获取完整的多行续写内容
   */
  getLastCompletionResult = async (): Promise<CompletionResult[] | null> => {
    if (!this.lastCompletionResult) {
      return null;
    }
    try {
      const res = [{
        code: this.lastCompletionResult.code.slice(this.codeCompletionOffset),
        completionLogprod: this.lastCompletionResult.completionLogprod,
        modelType: this.lastCompletionResult.modelType,
        modelVersion: this.lastCompletionResult.modelVersion,
      }];
      // 如果已经结束了，立马清理并返回，不需要做任何判断
      const isEnd = !this.getCollectionStatus().isCollecting;
      if (!isEnd) {
        // 如果没有结束，需要判断返回结果 trim之后是否有内容
        if (this.lastCompletionResult.code.slice(this.codeCompletionOffset).trim().length === 0) {
        // 没有内容，结束
          this.deferred = createDeferred();
          const code = await this.deferred.promise;
          this.deferred = null;
          res[0].code = code;
        }
      }
      this.codeCompletionOffset += res[0].code.length;
      if (isNumber(this.showIndex)) {
        this.showIndex++;
      }
      this.logger.info(`第${this.showIndex}次续写结果曝光: ${res[0].code}, 此时偏移量为${this.codeCompletionOffset}`, this.loggerScope);

      if (isEnd) {
      // onEnd执行了，结束
        this.cleanup();
      }
      return res;
    }
    catch (e) {
      return null;
    }
  };

  private tryResolveDeferred({
    sourceCode,
    isCompletion = false,
  }: {
    sourceCode: string;
    isCompletion?: boolean;
  }): void {
    if (this.deferred) {
      const code = sourceCode.slice(this.codeCompletionOffset);
      if (isCompletion) {
        this.deferred.resolve(code);
      }
      if (code.trim().length > 0) {
        this.deferred.resolve(code);
      }
    }
  }

  private tryRejectDeferred({
    reason,
  }: {
    reason: string;
  }): void {
    if (this.deferred) {
      this.deferred.reject(reason);
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.cancelCurrentRequest();
    this.backgroundCollector.cleanup();
    this.lastCompletionResult = null;
    // cleanup时不需要充值 showIndex, 请求发起时才需要
    // this.showIndex = undefined;
  }

  private getCompletionParams(doc: vscode.TextDocument, pos: vscode.Position) {
    const projectInfos = getProjectInfo();
    const project = projectInfos && projectInfos.length > 0 ? projectInfos[0] : undefined;
    this.requestId = uuidv4();
    return {
      requestId: this.requestId,
      codeBeforeCursor: doc.getText(
        new vscode.Range(new vscode.Position(0, 0), pos),
      ),
      codeAfterCursor: doc.getText(
        new vscode.Range(
          pos,
          doc.lineAt(doc.lineCount - 1).range.end,
        ),
      ),
      languageId: doc.languageId,
      fileName: doc.fileName,
      absolutePath: doc.fileName,
      cursorOffset: pos.character,
      deviceId: this.globalState.get(GlobalState.DEVICE_ID),
      maxNewTokens: this.config.get(Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION),
      modelType: this.config.get(Config.MODEL_TYPE),
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
      relativePath: getRelativePath(doc.fileName),

      projectName: project?.name,
      gitRepo: project?.gitRepo,
      gitRemote: project?.gitRemote,
      currentBranchName: project?.currentBranchName,
      gitUsername: project?.username,
      gitUserEmail: project?.userEmail,

      platform: PLUGIN_PLATFORM,
      pluginVersion: PLUGIN_VERSION,
      deviceName: this.device.deviceName,
      deviceModel: this.device.deviceModel,
      deviceOsVersion: this.device.deviceOsVersion,
      viewedDocuments: this.getRecentlyOpenedDoc(doc.fileName),
      stream: false,
      count: 3,

      // 20250617优化测试参数
      apiVersion: 20250617,
    };
  }

  private getRecentlyOpenedDoc(currentFileName: string): CommonDocument[] {
    try {
      const recentlyOpened = this.docManager.getRecentlyDocs(10);
      const res = [];
      if (recentlyOpened) {
        for (let i = recentlyOpened.length - 1; i >= 0; i--) {
          // 去重复
          if (recentlyOpened[i].fileName != currentFileName) {
            // 防止文件过大,限制在长度<100000
            const length = recentlyOpened[i].content?.length;
            if (length && length < 100000) {
              res.push(recentlyOpened[i]);
            }
          }
        }
      }
      return res;
    }
    catch (error) {
      return [];
    }
  }
}
