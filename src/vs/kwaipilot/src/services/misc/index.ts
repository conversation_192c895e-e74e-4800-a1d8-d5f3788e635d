import { ExtensionMiscShape } from "shared/lib/bridge/protocol";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { BridgeUploadFile, FileData, UploadFile } from "shared/lib/misc/file";
import * as vscode from "vscode";
import fs from "fs";
import { LoggerManager } from "../../base/logger";
import { upload } from "../../api/upload";
import { ConfigManager } from "../../base/state-manager";
import { Config } from "shared/lib/state-manager/types";
import { DefaultBaseUrl } from "../../const";
import path from "path";
import * as os from "os";
import { v4 as uuidv4 } from "uuid";

export class MiscService extends ServiceModule implements ExtensionMiscShape {
  constructor(ext: ContextManager) {
    super(ext);
  }

  private async openFileDialog(): Promise<BridgeUploadFile[]> {
    const files = await vscode.window
      .showOpenDialog({
        canSelectFiles: true,
        canSelectFolders: false,
        canSelectMany: true,
        filters: {
          Code: [
            "pdf", "docx", "txt", "doc",
            "js", "ts", "jsx", "tsx", // JavaScript/TypeScript
            "html", "css", "scss", "less", "sass", // Web
            "py", // Python
            "java", // Java
            "c", "cpp", // C/C++
            "cs", // C#
            "rb", // Ruby
            "go", // Go
            "rs", // Rust
            "swift", // Swift
            "php", // PHP
            "kt", // Kotlin
            "json", "xml", "yml", "yaml", // Data formats
            "sh", // Shell scripts
            "asm", // Assembly
            "bat", "cmd", // Batch
            "lisp", "el", "scm", "ss", "rkt", // Lisp
            "m", // MATLAB or Objective-C
            "pas", // Pascal
            "pl", "pm", "t", // Perl
            "r", "rdata", "rds", // R
            "scala", // Scala
            "sql", // SQL
            "tcl", // Tcl
            "vhd", "vhdl", // VHDL
            "v", "sv", // Verilog
            "Dockerfile", // Docker
            "Makefile", "makefile", "mk", // Makefile
            "lua", // Lua
            "hs", // Haskell
            "erl", "hrl", // Erlang
            "ex", "exs", // Elixir
            "rkt", // Racket
            "sass", // Sass
            "less", // Less
            "clj", "cljs", "cljc", // Clojure
            "coffee", // CoffeeScript
            "cr", // Crystal
            "dart", // Dart
            "fs", "fsi", "fsx", // F#
            "f", "f90", "f95", // Fortran
            "groovy", "gvy", "gy", "gsh", // Groovy
            "hx", // Haxe
            "jl", // Julia
            "ml", "mli", // OCaml
            "sol", // Solidity
            "vim", // Vim script
          ],
        },
      });
    if (!files) return [];
    return this.doUpload(files);
  }

  private async doUpload(files: vscode.Uri[]): Promise<BridgeUploadFile[]> {
    let addFileSize = 0;
    const res: BridgeUploadFile[] = [];

    for (let i = 0; i < files.length; i++) {
      await new Promise((resolve) => {
        fs.stat(files[i].path, (err, stats) => {
          if (err) {
            console.error(err);
            return;
          }
          addFileSize = addFileSize + stats.size;
          resolve(addFileSize);
        });
      });
    }
    const totalSize = /* XXX: fileSize 限制 fileSize +  */addFileSize;
    // 最大 3MB
    if (totalSize > 3145728) {
      this.logger.warn("file total size", "file", {
        value: totalSize,
      });
      vscode.window.showErrorMessage("上传文件总大小超过 3MB 上限，请调整后重试");
      return [];
    }
    // XXX: fileSize 限制? fileSize = fileSize + addFileSize;
    for (let i = 0; i < files.length; i++) {
      const uri = files[i];
      const f = await new Promise<UploadFile>((resolve) => {
        upload(this.getBase(ConfigManager).get(Config.PROXY_URL) || DefaultBaseUrl, {
          file: uri,
          onStart: () => {
          },
          onProgress: () => {
          },
          onSuccess: (data) => {
            resolve(data);
          },
          onFailed: (file) => {
            console.error(file);
          },
        });
      });
      res.push({
        uri: uri.toString(),
        relativePath: vscode.workspace.asRelativePath(uri),
        uploadInfo: f,
      });
    }
    return res;
  }

  public async saveFileToTemp(file: FileData): Promise<string> {
    // 创建临时目录
    const tempDir = path.join(os.tmpdir(), "kwaipilot-images");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // 生成唯一文件名
    const ext = file.name.split(".").pop();
    const fileName = `${uuidv4()}.${ext}`;
    const filePath = path.join(tempDir, fileName);

    // 将File对象写入文件
    // const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(file.data);
    fs.writeFileSync(filePath, buffer);

    return filePath;
  }

  private async uploadImageDirectly(files: vscode.Uri[], limit: number): Promise<BridgeUploadFile[]> {
    if (files.length > limit) {
      vscode.window.showErrorMessage("添加失败，最多添加 10 张图片");
      return [];
    }
    const res: BridgeUploadFile[] = [];
    for (let i = 0; i < files.length; i++) {
      const uri = files[i];
      const f = await new Promise<UploadFile>((resolve) => {
        upload(this.getBase(ConfigManager).get(Config.PROXY_URL) || DefaultBaseUrl, {
          file: uri,
          onStart: () => {
          },
          onProgress: () => {
          },
          onSuccess: (data) => {
            resolve(data);
          },
          onFailed: (file) => {
            console.error(file);
          },
        });
      });
      res.push({
        uri: uri.toString(),
        relativePath: vscode.workspace.asRelativePath(uri),
        uploadInfo: f,
      });
    }
    return res;
  }

  private async openImageDialog(limit: number): Promise<BridgeUploadFile[]> {
    const files = await vscode.window
      .showOpenDialog({
        canSelectFiles: true,
        canSelectFolders: false,
        canSelectMany: true,
        filters: {
          Images: ["png", "jpg", "jpeg", "svg", "PNG", "JPG", "JPEG", "SVG"],
        },
      });
    if (!files) return [];
    return this.uploadImageDirectly(files, limit);
  }

  $uploadFile(): Promise<BridgeUploadFile[]> {
    return this.openFileDialog();
  }

  $uploadImage(limit: number): Promise<BridgeUploadFile[]> {
    return this.openImageDialog(limit);
  }

  async $uploadImageDirectly(files: FileData[], limit: number): Promise<BridgeUploadFile[]> {
    // 先把文件保存在本地临时目录
    const savedFiles = await Promise.all(files.map(async (file) => {
      const filePath = await this.saveFileToTemp(file);
      return vscode.Uri.file(filePath);
    }));
    return this.uploadImageDirectly(savedFiles, limit);
  }

  get logger() {
    return this.getBase(LoggerManager);
  }
}
