import * as fs from 'original-fs';
import * as path from 'path';
import { VSCodePathUtils, transform2UnionMcpSettings } from '../utils/index.js';
import { WelcomeStorageService } from '../storage/welcomeStorage.js';
import { getDataFolderName, getProductNameShort } from '../utils/appName.js';
import { safeReadFileSync } from '../utils/file.js';
import { SQLiteStorageDatabase } from '../../../base/parts/storage/node/storage.js';
import { Storage } from '../../../base/parts/storage/common/storage.js';
import { DISABLED_EXTENSIONS_STORAGE_PATH } from '../../../platform/extensionManagement/common/extensionManagement.js';

// 插件标识符接口
interface IExtensionIdentifier {
    id: string;
    uuid?: string;
}

interface UserSettings {
    [key: string]: any;
}

export class ConfigManager {
    private readonly pathUtils: VSCodePathUtils;
    private readonly appHomedir: string;
    private readonly storageService: WelcomeStorageService;

    constructor() {
        const nameShort = getProductNameShort();
        const dataFolderName = getDataFolderName();
        this.pathUtils = new VSCodePathUtils(nameShort, dataFolderName);
        this.appHomedir = this.pathUtils.getHomeDir();
        this.storageService = new WelcomeStorageService();
    }

    /**
     * 获取存储服务实例
     */
    getStorageService(): WelcomeStorageService {
        return this.storageService;
    }

    async importConfig(nameShort: 'Code' | 'Cursor') {
        const sourcePathUtils = new VSCodePathUtils(nameShort);

        const configs = await this.readSourceConfigs(sourcePathUtils);
        await this.writeTargetConfigs(configs);
        try {
            await this.copyExtensions(sourcePathUtils);
        } catch (error) {
            // @IMP copy 失败 也不要阻止 后续的流程推进
            console.error('Failed to copy extensions:', error);
        }

        // 导入禁用插件状态
        try {
            await this.importDisabledExtensions(sourcePathUtils);
        } catch (error) {
            console.error('Failed to import disabled extensions:', error);
        }

        return {
            status: 'success',
            message: 'Settings imported successfully',
        };
    }

    private async readSourceConfigs(sourcePathUtils: VSCodePathUtils) {
        const paths = {
            userSettings: sourcePathUtils.getUserSettingsPath(),
            keybindings: sourcePathUtils.getKeybindingsPath(),
            extensions: sourcePathUtils.getExtensionsPath(),
            mcpSettings: sourcePathUtils.getMcpSettingsPath(),
        };

        const contents = {
            userSettings: safeReadFileSync(paths.userSettings, '{}'),
            keybindings: safeReadFileSync(paths.keybindings, '[]'),
            extensions: safeReadFileSync(paths.extensions, '[]'),
            mcpSettings: safeReadFileSync(paths.mcpSettings, '{}'),
        };

        return {
            paths,
            contents,
            mcpSettingsJSON: transform2UnionMcpSettings(sourcePathUtils.nameShort, JSON.parse(contents.mcpSettings))
        };
    }

    private async writeTargetConfigs(configs: Awaited<ReturnType<typeof this.readSourceConfigs>>) {
        const targetPaths = {
            userSettings: this.pathUtils.getUserSettingsPath(),
            keybindings: this.pathUtils.getKeybindingsPath(),
            extensions: this.pathUtils.getExtensionsPath(),
            // @IMP: 这里是共享的 插件的 mcp 设置，因此 这里的目录是固定的
            mcpSettings: path.resolve(this.appHomedir, './.kwaipilot/mcp/kwaipilot-mcp-settings.json')
        };

        // 确保目标目录存在
        Object.values(targetPaths).forEach(filePath => {
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });

        // 合并 MCP 设置
        const currentMcpSettings = safeReadFileSync(targetPaths.mcpSettings, '{}');
        const mergeMcpSettings = Object.assign(
            {},
            JSON.parse(currentMcpSettings),
            configs.mcpSettingsJSON
        );

        // 写入配置文件
        fs.writeFileSync(targetPaths.userSettings, configs.contents.userSettings);
        fs.writeFileSync(targetPaths.keybindings, configs.contents.keybindings);
        fs.writeFileSync(targetPaths.extensions, configs.contents.extensions);
        fs.writeFileSync(targetPaths.mcpSettings, JSON.stringify(mergeMcpSettings, null, 2));

        return targetPaths;
    }

    private async copyExtensions(sourcePathUtils: VSCodePathUtils) {
        //  ~/.vscode/extensions
        const extensionsDir = path.dirname(sourcePathUtils.getExtensionsPath());
        const targetDir = path.dirname(this.pathUtils.getExtensionsPath());

        if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
        }

        // 需要跳过 kuaishou.kwaipilot 开头的目录的复制
        const skipDirs = ['kuaishou.kwaipilot'];
        await this.copyDir(extensionsDir, targetDir, skipDirs);

        // 需要删除 ~/.vscode/extensions/extensions.json 里关于跳过文件的信息
        const extensionsJsonPath = this.pathUtils.getExtensionsPath();
        const extensionsJson = JSON.parse(safeReadFileSync(extensionsJsonPath, '{}'));
        const filteredExtensions = extensionsJson.filter((ext: any) => {
            return !skipDirs.some(id => id === ext.identifier.id);
        });
        fs.writeFileSync(extensionsJsonPath, JSON.stringify(filteredExtensions, null, 2));
    }

    /**
     * 导入禁用插件状态
     * 从源IDE的存储数据库中读取禁用插件信息，并写入到目标IDE的存储数据库中
     */
    private async importDisabledExtensions(sourcePathUtils: VSCodePathUtils): Promise<void> {
        try {
            // 读取源IDE的禁用插件状态
            const sourceDisabledExtensions = await this.readDisabledExtensionsFromStorage(sourcePathUtils);

            if (sourceDisabledExtensions && sourceDisabledExtensions.length > 0) {
                // 写入到一个临时的存储文件中
                this.storageService.store(DISABLED_EXTENSIONS_STORAGE_PATH, sourceDisabledExtensions);
                console.log(`Successfully imported ${sourceDisabledExtensions.length} disabled extensions from ${sourcePathUtils.nameShort}`);
            } else {
                console.log(`No disabled extensions found in ${sourcePathUtils.nameShort}`);
            }
        } catch (error) {
            console.error('Error importing disabled extensions:', error);
            throw error;
        }
    }

    /**
     * 从源IDE的存储数据库中读取禁用插件状态
     */
    private async readDisabledExtensionsFromStorage(sourcePathUtils: VSCodePathUtils): Promise<IExtensionIdentifier[]> {
        const storageDbPath = this.getStorageDbPath(sourcePathUtils);

        if (!fs.existsSync(storageDbPath)) {
            console.log(`Storage database not found at: ${storageDbPath}`);
            return [];
        }

        try {
            // 检查存储数据库文件是否存在
            const exists = await fs.existsSync(storageDbPath);

            if (!exists) {
                console.log(`Storage database not found at: ${storageDbPath}`);
                return [];
            }

            // 使用VSCode的SQLiteStorageDatabase和Storage服务
            const storageDatabase = new SQLiteStorageDatabase(storageDbPath, {
                logging: {
                    logError: (error) => console.error('Storage database error:', error)
                }
            });

            const storage = new Storage(storageDatabase);

            try {
                // 初始化storage
                await storage.init();

                // 读取禁用插件数据
                const disabledExtensionsValue = storage.get(DISABLED_EXTENSIONS_STORAGE_PATH);

                if (!disabledExtensionsValue) {
                    return [];
                }

                // 解析JSON数据
                const disabledExtensions = JSON.parse(disabledExtensionsValue);
                if (Array.isArray(disabledExtensions)) {
                    console.log(`Found ${disabledExtensions.length} disabled extensions from ${sourcePathUtils.nameShort}`);
                    return disabledExtensions;
                } else {
                    console.warn('Disabled extensions data is not an array');
                    return [];
                }
            } catch (parseError) {
                console.error('Failed to parse disabled extensions data:', parseError);
                return [];
            } finally {
                // 确保关闭storage连接
                await storage.close();
            }
        } catch (error) {
            console.error('Error reading disabled extensions from storage:', error);
            return [];
        }
    }

    /**
     * 获取存储数据库文件路径
     */
    private getStorageDbPath(pathUtils: VSCodePathUtils): string {
        return pathUtils.getGlobalStoragePath();
    }

    private async copyDir(src: string, dest: string, skipDirs: string[]) {
        try {
            const stat = fs.statSync(src);
            if (stat.isDirectory()) {
                if (!fs.existsSync(dest)) {
                    await fs.promises.mkdir(dest, { recursive: true });
                }
                const files = await fs.promises.readdir(src);
                for (const file of files) {
                    if (skipDirs.some(dir => file.includes(dir))) {
                        continue;
                    }
                    const srcPath = path.join(src, file);
                    const destPath = path.join(dest, file);
                    await this.copyDir(srcPath, destPath, skipDirs);
                }
            } else {
                // 需要判断下文件是否已经存在，需要删除
                if (fs.existsSync(dest)) {
                    fs.rmSync(dest, { recursive: true });
                }
                await fs.promises.copyFile(src, dest);
            }
        } catch (error) {
            console.error(`Error copying from ${src} to ${dest}:`, error);
            throw error;
        }
    }

    async setTheme(themeId: string) {
        const userSettingsPath = this.pathUtils.getUserSettingsPath();

        // 确保设置文件目录存在
        const settingsDir = path.dirname(userSettingsPath);
        if (!fs.existsSync(settingsDir)) {
            fs.mkdirSync(settingsDir, { recursive: true });
        }

        // 读取并更新设置
        let userSettings: UserSettings = {};
        try {
            const content = safeReadFileSync(userSettingsPath, '{}');
            userSettings = JSON.parse(content);
        } catch (error) {
            // 如果文件不存在或解析失败，使用空对象
        }

        userSettings['workbench.colorTheme'] = themeId;
        fs.writeFileSync(userSettingsPath, JSON.stringify(userSettings, null, 2));
    }
}

