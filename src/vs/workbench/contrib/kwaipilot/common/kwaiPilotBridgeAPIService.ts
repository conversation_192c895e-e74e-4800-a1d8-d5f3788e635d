/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { KwaiPilotBridge } from '../browser/kwaipilotBridge.js';
export const IKwaiPilotBridgeAPIService = createDecorator<IKwaiPilotBridgeAPIService>('kwaiPilotBridgeAPIService');

/**
 * KwaiPilot Bridge API 服务接口
 * 提供与 webview 端的完整通信能力
 */
export interface IKwaiPilotBridgeAPIService {
	bridge: KwaiPilotBridge;

	readonly _serviceBrand: undefined;

	/**
	 * 编辑器相关操作
	 */
	readonly editor: {
		/**
		 * 插入代码
		 * @param params 包含要插入的代码内容
		 */
		insertCode(params: { content: string }): void;

		/**
		 * 文件编辑
		 * @param params 文件编辑参数
		 */
		fileEdit(params: {
			filename: string;
			modelOutput: string;
			sessionId: string;
			chatId: string;
			applyId: string;
		}): Promise<undefined>;

		/**
		 * 显示代码差异
		 * @param params 代码差异参数
		 */
		showCodeDiff(params: {
			content: string;
			section: any; // CodeSection 类型
			fullPath: string;
		}): void;

		/**
		 * 打开文件到编辑区
		 * @param filepath 文件相对工作区的路径
		 * @param startLine 起始行号（可选）
		 * @param endLine 结束行号（可选）
		 */
		openFileToEditor(filepath: string, startLine?: number, endLine?: number): void;

		/**
		 * 打开文件到编辑器（可能是差异编辑器）
		 * @param filepath 文件路径
		 */
		openFileToEditorMaybeDiffEditor(filepath: string): void;

		/**
		 * 接受所有文件的差异更改
		 */
		acceptAllFileDiff(): void;

		/**
		 * 拒绝所有文件的差异更改
		 */
		rejectAllFileDiff(): void;

		/**
		 * 接受特定文件的差异更改
		 * @param filepath 文件路径
		 */
		acceptDiff(filepath: string): void;

		/**
		 * 拒绝特定文件的差异更改
		 * @param filepath 文件路径
		 */
		rejectDiff(filepath: string): void;

		/**
		 * 保持差异更改
		 * @param payload 可选参数
		 */
		keepDiff(payload?: { filepath?: string; abortChat?: boolean; filesStatus?: { [filepath: string]: "accepted" | "rejected" } }): Promise<any>;

		/**
		 * 撤销差异更改
		 * @param payload 可选参数
		 */
		undoDiff(payload?: { filepath?: string; abortChat?: boolean }): Promise<any>;

		/**
		 * 保存差异内容
		 * @param payload 保存参数
		 */
		saveDiff(payload: {
			ts: number;
			filepath: string;
			diffContent: any; // DiffContent 类型
		}): void;

		/**
		 * 应用文件
		 * @param payload 应用参数
		 */
		applyFile(payload: { message: any }): void; // InternalLocalMessage 类型
	};
}
