.kwaipilot-diff-footer {
	position: absolute;
	left: 50%;
	bottom: 18px;
	transform: translateX(-50%);
	z-index: 1000;
	background: var(--vscode-editorWidget-background);
	border-radius: 4px;
	border: 1px solid var(--vscode-commandCenter-inactiveBorder);
	padding: 0 6px;
	display: flex;
	align-items: center;
	gap: 2px;
	color: var(--vscode-editor-foreground);
	font-size: 11px;
	height: 32px;
	box-sizing: border-box;
}

.kwaipilot-diff-footer button {
	border: none;
	outline: none !important;
}

.kwaipilot-diff-footer .prev-next-container {
	display: inline-flex;
	align-items: center;
	gap: 4px;
	min-width: 100px;
}

.kwaipilot-diff-footer.kwaipilot-diff-footer-mini .prev-next-container {
	min-width: 72px;
}

.kwaipilot-diff-footer .prev-diff-btn {
	transform: rotate(180deg);
}

.kwaipilot-diff-footer .prev-diff-btn,
.kwaipilot-diff-footer .next-diff-btn {
	cursor: pointer;
	color: var(--vscode-icon-foreground);
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 16px;
	height: 16px;
	background: transparent;
	outline: none;
}

.kwaipilot-diff-footer .prev-diff-btn .codicon,
.kwaipilot-diff-footer .next-diff-btn .codicon {
	outline: none;
}

.kwaipilot-diff-footer .diff-index-label {
	color: var(--vscode-editor-foreground);
	font-size: 11px;
	min-width: 30px;
	text-align: center;
}

.kwaipilot-diff-footer .accept-file-btn {
	margin-left: 4px;
	cursor: pointer;
	height: 20px;
	display: inline-flex;
	align-items: center;
	padding: 0 6px;
	border-radius: 2px;
	color: var(--vscode-button-foreground);
	cursor: pointer;
	background: var(--vscode-button-background);
	font-size: 11px;
}

.kwaipilot-diff-footer .accept-file-btn:hover {
	background: var(--vscode-button-hoverBackground);
}


.kwaipilot-diff-footer .reject-file-btn {
	height: 20px;
	display: inline-flex;
	align-items: center;
	padding: 0 6px;
	border-radius: 2px;
	color: var(--vscode-foreground);
	cursor: pointer;
	background: transparent;
	outline: none;
	font-size: 11px;
}

.kwaipilot-diff-footer .reject-file-btn:hover {
	color: var(--vscode-editor-foreground);
}

.kwaipilot-diff-footer.kwaipilot-diff-footer-mini .accept-file-btn .shortcut-text,
.kwaipilot-diff-footer.kwaipilot-diff-footer-mini .reject-file-btn .shortcut-text {
	display: none;
}

.kwaipilot-diff-footer .accept-file-btn .shortcut-text,
.kwaipilot-diff-footer .reject-file-btn .shortcut-text {
	color: var(--vscode-foreground);
	opacity: 0.5;
	font-size: 11px;
	margin-left: 4px;
	font-family: Inter;
}

.kwaipilot-diff-footer .accept-file-btn .shortcut-text {
	color: var(--vscode-button-foreground);
}

.kwaipilot-diff-footer .prev-file-btn,
.kwaipilot-diff-footer .next-file-btn {
	cursor: pointer;
	color: var(--vscode-icon-foreground);
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 16px;
	height: 16px;
	background: transparent;
}

.kwaipilot-diff-footer .prev-file-btn .codicon {
	transform: rotate(90deg);
	outline: none;
}

.kwaipilot-diff-footer .next-file-btn .codicon {
	transform: rotate(-90deg);
	outline: none;
}

.kwaipilot-review-next-file-footer {
	position: absolute;
	left: 50%;
	width: 120px;
	height: 32px;
	bottom: 18px;
	transform: translateX(-50%);
	z-index: 1000;
	background: var(--vscode-editorWidget-background);
	border-radius: 4px;
	border: 1px solid var(--vscode-commandCenter-inactiveBorder);
	padding: 0 6px;
	color: var(--vscode-foreground);
	font-size: 11px;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
}

.kwaipilot-review-next-file-footer button {
	border: none;
}

.kwaipilot-review-next-file-footer .review-next-file-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 2px;
	outline: none;
	font-size: 11px;
	padding: 0;
	color: var(--vscode-foreground);
	background: var(--vscode-editorWidget-background);
}

.kwaipilot-review-next-file-footer .review-next-file-btn:hover {
	color: var(--vscode-editor-foreground);
}

.kwaipilot-review-next-file-footer .codicon {
	transform: rotate(-90deg);
	outline: none;
}

.kwaipilot-diff-container {
	position: relative;
	width: 100%;
}

.kwaipilot-diff-chunk-btns-floating {
	visibility: hidden !important;
}

.kwaipilot-diff-chunk-btns-floating.hovered {
	visibility: inherit !important;
}

.kwaipilot-diff-chunk-btns.kwaipilot-diff-chunk-btns-mini .shortcut-text {
	display: none;
}

.kwaipilot-diff-chunk-btns {
	display: inline-flex !important;
	gap: 4px;
	background-color: var(--vscode-editorWidget-background);
	padding: 2px;
	position: absolute !important;
	left: auto !important;
	border-radius: 4px;
}

.kwaipilot-diff-chunk-accept-btn,
.kwaipilot-diff-chunk-reject-btn {
	padding: 0 6px;
	border: 1px solid var(--vscode-button-border);
	border-radius: 2px;
	cursor: pointer;
	font-size: 10px;
	height: 18px;
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 60px;
	gap: 2px;
}

.kwaipilot-diff-chunk-accept-btn {
	background-color: var(--vscode-charts-green);
	color: var(--vscode-editor-background);
}

.kwaipilot-diff-chunk-reject-btn {
	background-color: var(--vscode-charts-red);
	color: var(--vscode-textBlockQuote-background);
}

/* 确保 diff 内容区域不会被按钮遮挡 */
.chat-editing-original-zone,
.kwaipilot-diff-insert-zone {
	margin-right: 120px; /* 给按钮预留空间 */
}

/* 插入的代码块样式 */
.kwaipilot-diff-insert-lines {
	margin: 0;
	padding: 8px;
	background-color: var(--vscode-diffEditor-insertedLineBackground);
	border-left: 3px solid var(--vscode-diffEditor-insertedLineBackground);
}

/* 删除的代码块样式 */
.line-delete {
	background-color: var(--vscode-diffEditor-removedLineBackground);
	border-left: 3px solid var(--vscode-diffEditor-removedLineBackground);
}
