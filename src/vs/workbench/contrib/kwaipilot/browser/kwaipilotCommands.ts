// 命令常量
export const enum KwaipilotCommands {
	// IDE -> Plugin
	ShowDiff = 'kwaipilot.editCode.showDiff',
	AcceptDiff = 'kwaipilot.editCode.acceptDiff',
	RejectDiff = 'kwaipilot.editCode.rejectDiff',
	UpdateDiff = 'kwaipilot.editCode.updateDiff',
	AcceptAllDiff = 'kwaipilot.editCode.acceptAllDiff',
	RejectAllDiff = 'kwaipilot.editCode.rejectAllDiff',

	// Plugin -> IDE
	KeepDiff = 'kwaipilot.editCode.keepDiff',
	UndoDiff = 'kwaipilot.editCode.undoDiff',
	RequestShowDiff = 'kwaipilot.editCode.requestShowDiff',
	ClearAllDiffState = 'kwaipilot.editCode.clearAllDiffState'
}
