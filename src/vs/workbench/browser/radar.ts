import { importAMDNodeModule } from '../../amdX.js';
import product from '../../platform/product/common/product.js';

// 使用动态导入和错误处理来避免模块解析问题
let radarInstance: any = null;

// 动态导入 @ks-radar/electron 模块
async function initializeRadar() {
	try {
		// 尝试动态导入
		const RadarModule = await importAMDNodeModule<typeof import('@ks-radar/electron')>('@ks-radar/electron', 'dist/index.js');
		// 处理不同的导出格式
		const Radar = RadarModule;
		const RadarClass = Radar.default || Radar;

		console.log('radar#product info', product.commit, product.appVersion);

		// 创建 Radar 实例
		radarInstance = new RadarClass({
			// 【必填】
			projectId: '0ecc330179',
			// 【选填】添加与主进程一致的公共维度配置
			// commonDimensions: {
			// userId 和 deviceId 将通过主进程同步
			// versionName 和 product 将由应用自动获取
			// },
			// 【选填】
			customDimensions: {
				release_tag: product.commit || 'unknown', // 使用 commit ID 作为发布标签
				product_version: product.appVersion || 'unknown', // 使用 appVersion 作为产品版本号
				c_dimension1: product.quality || 'dev', // 使用 quality 作为质量版本号
			},
			// 【选填】
			// errorHook(errorData) {
			// 	console.log(errorData);
			// 	return {
			// 		// allow-any-unicode-next-line
			// 		msg: '自定义msg' + errorData.msg,
			// 		// allow-any-unicode-next-line
			// 		stack: '这块应该是无效的传入',
			// 		// allow-any-unicode-next-line
			// 		file: '自定义文件名' + errorData.file,
			// 		line: 222,
			// 		col: 333,
			// 	};
			// },
			// 【选填】
			// APIHook(apiData: any) {
			// 	const { request, response } = apiData;
			// 	console.log(apiData, 'request===', request, 'response===', response);
			// 	const data = JSON.parse(response.data);
			// 	return {
			// 		response_code: 123,
			// 		response_msg: data.result, // 业务定义的接口返回code的语义化解释
			// 		status: response.status,
			// 		custom_failed: false,
			// 	};
			// },
			// 【选填】
			// isIgnoreInvalidStatusCode: async (data: any) => {
			// 	console.log('isIgnoreInvalidStatusCode', data);
			// 	return false;
			// }
		});


		radarInstance.event({
			name: 'RADAR_APP_START', // 必填
		});

		console.log('Radar initialized successfully');
	} catch (error) {
		console.warn('Failed to initialize @ks-radar/electron:', error);
		// 创建一个空的占位实例，避免后续调用出错
		radarInstance = {
			// 提供基本的空实现
			track: () => { },
			error: () => { },
			event: () => { },
			setDimensions: () => { },
			customStage: () => { },
			fmp: () => { },
			// 可以根据需要添加更多方法
		};
	}
}

// 异步初始化
initializeRadar();

// 导出获取实例的函数
export function getRadarInstance() {
	return radarInstance;
}

// 为了兼容现有代码，仍然设置全局变量
Object.defineProperty(globalThis, '_RADAR_INSTANCE_', {
	get: () => radarInstance,
	configurable: true
});
