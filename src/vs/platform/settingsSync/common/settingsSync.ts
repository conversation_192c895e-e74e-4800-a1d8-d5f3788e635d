/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { createDecorator } from '../../../platform/instantiation/common/instantiation.js';

export const ISettingsSyncService = createDecorator<ISettingsSyncService>('settingsSyncService');

export interface ISettingsSyncService {
	readonly _serviceBrand: undefined;

	/**
	 * 读取用户设置内容
	 */
	readUserSettings(): Promise<string>;

	/**
	 * 验证设置内容
	 */
	validateSettings(content: string): Promise<boolean>;

	/**
	 * 导入设置
	 */
	importSettings(name: string): Promise<boolean>;

	/**
	 *
	 * 导入 mcp 设置
	 */
	importMcpSettings(name: string): Promise<boolean>;

	/**
	 * 导入禁用插件状态
	 */
	importDisabledExtensions(name: string): Promise<boolean>;
}

/**
 * 禁用插件读取服务 - 只在Node.js环境中可用
 */
export const IDisabledExtensionsReaderService = createDecorator<IDisabledExtensionsReaderService>('disabledExtensionsReaderService');

export interface IDisabledExtensionsReaderService {
	readonly _serviceBrand: undefined;

	/**
	 * 从源IDE的SQLite存储数据库中读取禁用插件状态
	 */
	readDisabledExtensions(ideType: string): Promise<IExtensionIdentifier[]>;
}

export interface IExtensionIdentifier {
	id: string;
	uuid?: string;
}

export class SettingsSyncError extends Error {
	constructor(
		public readonly code: SettingsSyncErrorCode,
		message: string,
		public readonly originalError?: Error
	) {
		super(message);
		this.name = 'SettingsSyncError';

		if (originalError) {
			this.stack = originalError.stack;
		}
	}
}

export enum SettingsSyncErrorCode {
	FileNotFound = 'FileNotFound',
	InvalidContent = 'InvalidContent',
	UnknownError = 'UnknownError'
}
