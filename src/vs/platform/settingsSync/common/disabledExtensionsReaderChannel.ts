/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { IChannel, IServerChannel } from '../../../base/parts/ipc/common/ipc.js';
import { IDisabledExtensionsReaderService, IExtensionIdentifier } from './settingsSync.js';

export const DISABLED_EXTENSIONS_READER_CHANNEL_NAME = 'disabledExtensionsReader';

/**
 * IPC通道服务端 - 在主进程中运行
 */
export class DisabledExtensionsReaderChannel implements IServerChannel {

	constructor(private service: IDisabledExtensionsReaderService) { }

	listen(_: unknown, event: string): never {
		throw new Error(`Event not found: ${event}`);
	}

	call(_: unknown, command: string, arg?: any): Promise<any> {
		switch (command) {
			case 'readDisabledExtensions': return this.service.readDisabledExtensions(arg);
		}
		throw new Error(`Call not found: ${command}`);
	}
}

/**
 * IPC通道客户端 - 在渲染进程中运行
 */
export class DisabledExtensionsReaderChannelClient implements IDisabledExtensionsReaderService {

	declare readonly _serviceBrand: undefined;

	constructor(private channel: IChannel) { }

	readDisabledExtensions(ideType: string): Promise<IExtensionIdentifier[]> {
		return this.channel.call('readDisabledExtensions', ideType);
	}
}
