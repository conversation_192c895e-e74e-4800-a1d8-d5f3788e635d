/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { URI } from '../../../base/common/uri.js';
import { ILogService } from '../../log/common/log.js';
import { IFileService } from '../../files/common/files.js';
import { VSCodeIdeType, VSCodePathUtils } from '../../environment/common/environmentService.js';
import { IDisabledExtensionsReaderService, IExtensionIdentifier } from '../common/settingsSync.js';
import { SQLiteStorageDatabase } from '../../../base/parts/storage/node/storage.js';
import { Storage } from '../../../base/parts/storage/common/storage.js';
import { DISABLED_EXTENSIONS_STORAGE_PATH } from '../../extensionManagement/common/extensionManagement.js';

/**
 * Node.js specific implementation for reading disabled extensions from SQLite database
 */
export class DisabledExtensionsReaderService implements IDisabledExtensionsReaderService {

	declare readonly _serviceBrand: undefined;

	constructor(
		@ILogService private readonly logService: ILogService,
		@IFileService private readonly fileService: IFileService
	) {
		this.logService.info('DisabledExtensionsReaderService created successfully');
	}

	/**
	 * 从源IDE的SQLite存储数据库中读取禁用插件状态
	 * 使用VSCode的Storage服务而不是直接操作SQLite
	 */
	async readDisabledExtensions(ideType: string): Promise<IExtensionIdentifier[]> {
		const name = ideType as VSCodeIdeType;
		try {
			const pathUtils = new VSCodePathUtils(name);
			// 构建存储数据库路径: {用户数据目录}/User/globalStorage/state.vscdb
			const userDataPath = pathUtils.getUserDataPath();
			const storageDbPath = `${userDataPath}/User/globalStorage/state.vscdb`;

			// 检查存储数据库文件是否存在
			const storageDbUri = URI.file(storageDbPath);
			const exists = await this.fileService.exists(storageDbUri);

			if (!exists) {
				this.logService.info(`Storage database not found at: ${storageDbPath}`);
				return [];
			}

			// 使用VSCode的SQLiteStorageDatabase和Storage服务
			const storageDatabase = new SQLiteStorageDatabase(storageDbPath, {
				logging: {
					logError: (error) => this.logService.error('Storage database error:', error)
				}
			});

			const storage = new Storage(storageDatabase);

			try {
				// 初始化storage
				await storage.init();

				// 读取禁用插件数据
				const disabledExtensionsValue = storage.get(DISABLED_EXTENSIONS_STORAGE_PATH);

				if (!disabledExtensionsValue) {
					return [];
				}

				// 解析JSON数据
				const disabledExtensions = JSON.parse(disabledExtensionsValue);
				if (Array.isArray(disabledExtensions)) {
					this.logService.info(`Found ${disabledExtensions.length} disabled extensions from ${name}`);
					return disabledExtensions;
				} else {
					this.logService.warn('Disabled extensions data is not an array');
					return [];
				}
			} catch (parseError) {
				this.logService.error('Failed to parse disabled extensions data:', parseError);
				return [];
			} finally {
				// 确保关闭storage连接
				await storage.close();
			}
		} catch (error) {
			this.logService.error('Error reading disabled extensions from storage:', error);
			return [];
		}
	}
}
