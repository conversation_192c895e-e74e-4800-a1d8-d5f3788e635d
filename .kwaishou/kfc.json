{"variables": {"baseBranch": "master", "checklistConfig": {"options": [{"value": "version", "label": "检查是版本号是否已更新"}], "link": ""}}, "formInitValues": {"Gitlab": {"featureIterationType": "prefix", "featureIterationName": "release/", "featureChangeType": "prefix", "featureChangeName": "feat/", "fixedStagingBranchName": "delopy/staging"}, "Radar": {"BoardViewType": "cur"}, "检查项": {"checklistConfigOptions": "context.variables.checklistConfig.options"}, "消息推送": {"kimGroupId": []}}, "appTypes": [{"name": "Node", "enableCreate": false, "flowInfo": [{"flowName": "multi_change_iteration", "stages": [{"stageName": "multi_change_iteration_pre", "jobs": [{"name": "pre_cloud_deploy", "defaultHide": false}]}, {"stageName": "multi_change_iteration_prod", "jobs": [{"name": "multi_change_iteration_prod_cloud_deploy", "defaultHide": false}]}]}]}, {"name": "Web", "flowInfo": [{"flowName": "multi_change_iteration", "stages": [{"stageName": "multi_change_iteration_prod", "jobs": [{"name": "multi_change_iteration_prod_kfx", "defaultHide": false}]}]}]}], "forms": {"Gitlab": {"forms": {"blockTitle": {"type": "void", "x-component": "BlockTitle", "x-component-props": {"title": "分支设置"}}, "container": {"type": "void", "properties": {"title": {"type": "void", "x-component": "Title", "x-component-props": {"title": "迭代分支"}}, "featureIterationType": {"type": "string", "title": "分支名称", "enum": [{"label": "无限定", "value": "norule"}, {"label": "限定前缀", "value": "prefix"}], "required": true, "x-component": "Radio.Group", "x-decorator": "FormItem"}, "featureIterationName": {"type": "string", "title": "迭代分支", "x-component": "Input", "x-decorator": "FormItem", "x-reactions": {"dependencies": [".featureIterationType"], "fulfill": {"state": {"visible": "{{ $deps[0] !== 'norule' }}"}}}}, "title2": {"type": "void", "x-component": "Title", "x-component-props": {"title": "变更分支"}}, "featureChangeType": {"type": "string", "title": "分支名称", "enum": [{"label": "无限定", "value": "norule"}, {"label": "限定前缀", "value": "prefix"}], "required": true, "x-component": "Radio.Group", "x-decorator": "FormItem"}, "featureChangeName": {"type": "string", "title": "变更分支", "x-component": "Input", "x-decorator": "FormItem", "x-reactions": {"dependencies": [".featureChangeType"], "fulfill": {"state": {"visible": "{{ $deps[0] !== 'norule' }}"}}}}, "fixedStagingBranchName": {"type": "string", "title": "固定测试分支", "x-component": "Input", "x-decorator": "FormItem"}, "baseBranch": {"type": "string", "title": "基线分支", "x-component": "Input", "x-decorator": "FormItem"}}, "x-component": "KfcGroup", "x-component-props": {"title": "需求开发"}}, "mergeBaseTitle": {"type": "void", "x-component": "BlockTitle", "x-component-props": {"title": "合入基线分支设置"}}, "mergeBaseContainer": {"type": "void", "properties": {"autoMerge": {"type": "boolean", "title": "是否自动回写基线", "x-decorator": "FormItem", "x-component": "KCheckbox"}, "isDeleteSourceBranch": {"type": "boolean", "title": "回写基线时是否自动删除源分支", "x-decorator": "FormItem", "x-component": "KCheckbox"}}, "x-component": "KfcGroup", "x-component-props": {"title": "需求开发"}}}, "component": {}}, "Team": {"multiRepoConfigMode": "app", "forms": {"linkTitle": {"type": "void", "x-component": "BlockTitle", "x-component-props": {"title": "关联项目"}}, "TeamProjectId": {"type": "string", "x-decorator": "FormItem", "title": "项目名称", "x-component": "TeamSelect", "required": true, "x-kfc-auth-field": true}, "FeatureTaskClass": {"type": "string", "title": "变更绑定", "x-decorator": "FormItem", "x-component": "TeamClassSelect", "x-component-props": {"title": "需求开发"}, "required": true, "x-reactions": {"dependencies": [".TeamProjectId"], "fulfill": {"schema": {"x-component-props": {"teamProjectId": "{{ $deps[0] }}"}}}}}, "TeamConvertTable": {"type": "void", "x-component": "TeamConvertTable", "x-component-props": {"applicationId": "context.app.id"}, "x-reactions": {"dependencies": [".TeamProjectId", ".FeatureTaskClass"], "fulfill": {"schema": {"x-component-props": {"teamProjectId": "{{ $deps[0] }}", "teamClasses": "{{ [{ teamClasses: $deps[1] }] }}"}}}}}}, "component": {}}, "Pipeline": {"multiRepoConfigMode": "repo", "forms": {"linkTitle": {"type": "void", "x-component": "BlockTitle", "x-component-props": {"title": "流水线配置"}}, "testPipelinesTitle": {"type": "void", "x-component": "KText", "x-component-props": {"content": "STAGING环境流水线，使用在「开发阶段」、「测试阶段」"}, "x-decorator-props": {"feedbackLayout": "terse"}, "x-decorator": "FormItem", "title": "使用场景"}, "testPipelines": {"x-component": "PipelineConfigSelect", "x-decorator": "FormItem", "title": "流水线", "required": true, "x-validator": {"pipelineConfigNullableCheck": true}, "x-kfc-auth-field": true}, "prePipelinesTitle": {"type": "void", "x-component": "KText", "x-component-props": {"content": "PRT环境流水线，使用在「预发阶段」"}, "x-decorator-props": {"feedbackLayout": "terse"}, "x-decorator": "FormItem", "title": "使用场景"}, "prePipelines": {"x-component": "PipelineConfigSelect", "x-decorator": "FormItem", "title": "流水线", "required": true, "x-validator": {"pipelineConfigNullableCheck": true}, "x-kfc-auth-field": true}, "onlinePipelinesTitle": {"type": "void", "x-component": "KText", "x-component-props": {"content": "线上环境（PROD)环境流水线，使用在上线阶段"}, "x-decorator-props": {"feedbackLayout": "terse"}, "x-decorator": "FormItem", "title": "使用场景"}, "onlinePipelines": {"x-component": "PipelineConfigSelect", "x-decorator": "FormItem", "title": "流水线", "required": true, "x-validator": {"pipelineConfigNullableCheck": true}, "x-kfc-auth-field": true}}, "component": {}}, "容器云": {"forms": {"linkTitle": {"type": "void", "x-component": "BlockTitle", "x-component-props": {"title": "容器云配置"}}, "cloudDeployNode": {"x-component": "HaloCloudServiceTree", "x-component-props": {}, "x-decorator": "FormItem", "required": true, "title": "部署服务节点", "x-kfc-auth-field": true}}, "component": {}}, "Radar": {"forms": {"linkTitle": {"type": "void", "x-component": "BlockTitle", "x-component-props": {"title": "关联项目"}}, "RadarProject": {"type": "string", "x-decorator": "FormItem", "title": "项目名称", "x-component": "RadarProjectSelect", "required": true, "x-kfc-auth-field": true}, "linkTitle2": {"type": "void", "x-component": "BlockTitle", "x-component-props": {"title": "监控看板"}, "x-reactions": {"dependencies": [".RadarProject"], "fulfill": {"state": {"visible": "{{ !!$deps[0]?.[0]  }}"}}}}, "RadarPanel": {"type": "string", "x-decorator": "FormItem", "title": "", "x-component": "RadarDashBoard", "required": true, "x-kfc-auth-field": true, "x-component-props": {"tmpId": 20003}, "x-reactions": {"dependencies": [".RadarProject"], "fulfill": {"schema": {"x-component-props": {"projectIds": "{{ $deps[0] }}"}, "x-visible": "{{ !!$deps[0]?.[0]  }}"}}}}, "linkTitle3": {"type": "void", "x-component": "BlockTitle", "x-component-props": {"title": "看板配置"}, "x-reactions": {"dependencies": [".RadarPanel"], "fulfill": {"state": {"visible": "{{ !!$deps[0]?.[0]  }}"}}}, "x-description-component-for": "BoardViewType"}, "BoardViewType": {"type": "string", "title": "观测维度", "enum": [{"label": "默认", "value": "default"}, {"label": "按 “产品版本号” 维度观测", "value": "cur"}, {"label": "按 “发布标签” 维度观测", "value": "gray"}], "default": "default", "required": false, "x-component": "Radio.Group", "x-decorator": "FormItem", "x-reactions": {"dependencies": [".RadarPanel"], "fulfill": {"state": {"visible": "{{ !!$deps[0]?.[0]  }}"}}}}}, "component": {}}, "KFX": {"forms": {"kfxAppId": {"type": "string", "x-decorator": "FormItem", "title": "KFX应用Id", "x-component": "KFXSelect", "required": true}, "kfxCookieKey": {"type": "string", "x-decorator": "FormItem", "title": "白名单方式(<PERSON><PERSON> 键)", "x-component": "Input", "x-component-props": {"placeholder": "用于精确匹配的Cookie键，建议使用userId或did"}}, "kfxCookieValue": {"type": "string", "x-decorator": "FormItem", "title": "白名单方式(<PERSON><PERSON> 值)", "x-component": "Input.TextArea", "x-component-props": {"placeholder": "用于精确匹配的Cookie值，多个可选值用英文逗号分割，注意不要用空格和换行符"}}}, "component": {}}, "检查项": {"multiRepoConfigMode": "app", "forms": {"checklistConfigOptions": {"title": "", "x-decorator": "FormItem", "required": true, "x-component-props": {"title": "上线检查项", "defaultValue": "context.variables.checklistConfig.options"}, "x-component": "CheckList"}}, "component": {}}, "消息推送": {"forms": {"kimGroupId": {"x-component": "KimGroupSelect", "x-decorator": "FormItem", "title": "消息推送群组"}}, "component": {}}}, "flows": [{"name": "multi_change", "displayName": "需求开发", "parentFlowName": "multi_change_iteration", "type": "change", "forms": {"component": {}, "forms": {"layout": {"type": "void", "x-component": "FormLayout", "x-component-props": {"layout": "vertical"}, "properties": {"taskIds": {"type": "array", "title": "Team任务", "x-component": "TeamTaskSelect", "x-component-props": {"projectId": "context.app.formValues.Team.TeamProjectId", "taskClasses": "context.app.formValues.Team.FeatureTaskClass"}, "x-decorator": "FormItem", "required": true}, "slot": {"type": "void", "x-component": "VoidSlot", "x-component-props": {"id": "iteration-change-name"}}, "branchName": {"type": "string", "title": "变更分支", "x-component": "BranchSelect", "x-component-props": {"type": "context.app.formValues.Gitlab.featureChangeType", "name": "context.app.formValues.Gitlab.featureChangeName", "gitlabId": "context.app.gitlabId", "branchSelectTypeStoreKey": "change-branch"}, "x-validator": {"gitSameBranchCheck": true, "gitlabId": "context.app.gitlabId", "branchSelectTypeStoreKey": "change-branch"}, "x-decorator": "FormItem", "required": true, "x-reactions": {"dependencies": [".taskIds"], "fulfill": {"schema": {"x-component-props": {"default": "{{ $deps[0]?.[0] }}"}}}}}}}}}, "dependences": {"mergeToParentFlow": "multi_change_pre_mr"}, "stages": [{"name": "multi_change_dev", "displayName": "开发阶段", "jobs": [{"name": "multi_change_dev_checkout", "run": "@kfc/gitlab-checkout:0.0.1", "groupStrategy": "XOR", "displayName": "切换分支", "input": {"sourceBranch": "context.formValues.app.Gitlab.baseBranch", "targetBranch": "context.variables.branchName"}}, {"name": "multi_change_dev_team_start", "run": "@kfc/team-status:0.0.3", "displayName": "Team 状态翻转 (开始开发)", "input": {"status": "'TASK_BRANCH_BIND'", "forceRequiredReady": true}, "extra": {"convertInfo": "变更创建完成"}, "groupStrategy": true}, {"name": "dev_mr", "run": "@kfc/kdev-mr:0.0.1", "groupStrategy": "XOR", "allowHide": true, "displayName": "合入测试分支", "input": {"sourceBranch": "context.variables.branchName", "targetBranch": "context.formValues.app.Gitlab.fixedStagingBranchName"}}, {"run": "@halo/pipeline:1.0.2", "name": "dev_pipeline", "allowHide": true, "displayName": "开发部署", "input": {"showHistoryByJob": true, "branchName": "context.formValues.app.Gitlab.fixedStagingBranchName", "details": "context.formValues.app.Pipeline.testPipelines", "hookStrategy": "'running-stage'", "mrUseRunningStageStrategy": true}, "watch": {"mrAutoBuild": {"value": "context.jobs.dev_mr.state.isMerged", "expectValue": true, "immediate": true}}, "condition": "context.jobs.dev_mr.withSkippedState(true).isMerged"}, {"name": "multi_change_cr", "run": "@kfc/kdev-mr:0.0.1", "groupStrategy": "XOR", "displayName": "代码评审（CR）", "input": {"sourceBranch": "context.variables.branchName", "targetBranch": "context.parent ? context.parent.variables.branchName : context.formValues.app.Gitlab.baseBranch", "crType": "'ca'"}}, {"run": "@kfc/test-plan:0.0.3", "name": "dev_test_plan", "groupStrategy": true, "displayName": "开发自测", "input": {"branchName": "context.variables.branchName", "teamId": "context.variables.taskIds", "autoRelate": true}}, {"name": "multi_change_dev_confirm", "run": "@kfc/confirm:0.0.2", "displayName": "确认开发完成", "input": {"beforeConfirmText": "'请确认开发完成，确认后将进入测试阶段'", "confirmedText": "'已确认开发完成'", "disableCancelBtn": true}, "condition": "context.jobs.dev_pipeline.withSkippedState('FINISHED').status === 'FINISHED' && (context.jobs.multi_change_cr.state.isCreated || context.jobs.multi_change_cr.state.isMerged) && context.jobs.dev_test_plan.withSkippedState('FINISHED').status === 'FINISHED'", "groupStrategy": true}], "checkpoint": "context.jobs.multi_change_dev_confirm.state.isConfirm", "checkpointMessages": [{"jobName": "multi_change_dev_confirm", "condition": "!context.jobs.multi_change_dev_confirm.state.isConfirm", "message": "请点击确认开发完成"}]}, {"name": "multi_change_test", "displayName": "测试阶段", "jobs": [{"name": "multi_change_test_mr", "run": "@kfc/kdev-mr:0.0.1", "groupStrategy": "XOR", "allowHide": true, "displayName": "合入测试分支", "input": {"sourceBranch": "context.variables.branchName", "targetBranch": "context.formValues.app.Gitlab.fixedStagingBranchName"}}, {"run": "@halo/pipeline:1.0.2", "name": "multi_change_test_pipeline", "allowHide": true, "displayName": "测试部署", "input": {"showHistoryByJob": true, "branchName": "context.formValues.app.Gitlab.fixedStagingBranchName", "details": "context.formValues.app.Pipeline.testPipelines"}, "watch": {"mrAutoBuild": {"value": "context.jobs.multi_change_test_mr.state.isMerged", "expectValue": true, "immediate": true}}, "condition": "context.jobs.multi_change_test_mr.withSkippedState(true).isMerged"}, {"name": "multi_change_test_sheet", "run": "@kfc/test-sheet:0.0.1", "allowSkip": true, "defaultSkip": false, "displayName": "发起提测", "input": {"branchName": "context.variables.branchName", "teamIds": "context.variables.taskIds", "taskStatusTeamNameMap": "({14: 'SUBM<PERSON>_QA', 1: 'QA_ADMIT_OK', 7: 'QA_ADMIT_OK', 5: 'QA_CONFIRM_OK', 9:'QA_CONFIRM_OK'})"}, "groupStrategy": true}, {"name": "multi_change_test_team_submit", "displayName": "Team 状态翻转 (提测完成)", "run": "@kfc/team-status:0.0.3", "showInFlow": true, "input": {"status": "'SUBMIT_QA'", "skipPreCheck": true}, "extra": {"convertInfo": "发起提测"}, "condition": "context.jobs.multi_change_test_sheet.withSkippedState(14).taskStatus === 14", "groupStrategy": true}, {"name": "multi_change_test_in", "displayName": "Team 状态翻转 (测试准入)", "run": "@kfc/team-status:0.0.3", "input": {"status": "'QA_ADMIT_OK'", "skipPreCheck": true}, "extra": {"convertInfo": "测试准入"}, "condition": "context.jobs.multi_change_test_team_submit.state.noRequired && (context.jobs.multi_change_test_sheet.withSkippedState(1).taskStatus === 1 || context.jobs.multi_change_test_sheet.withSkippedState(1).taskStatus === 7)", "groupStrategy": true}, {"run": "@kfc/test-plan:0.0.3", "name": "test_test_plan", "groupStrategy": true, "displayName": "测试计划", "input": {"branchName": "context.variables.branchName", "teamId": "context.variables.taskIds", "autoRelate": true}}, {"name": "multi_change_team_bug_list", "run": "@kfc/team-buglist:0.0.2", "displayName": "缺陷列表", "input": {"teamId": "context.variables.taskIds[0]"}, "groupStrategy": true}, {"name": "multi_change_test_team_confirm_ok", "run": "@kfc/team-status:0.0.3", "displayName": "Team 状态翻转 (测试完成)", "extra": {"convertInfo": "确认测试完成"}, "input": {"status": "'QA_CONFIRM_OK'", "skipPreCheck": true}, "condition": "context.jobs.multi_change_test_in.state.noRequired && (context.jobs.multi_change_test_sheet.withSkippedState(5).taskStatus === 5 || context.jobs.multi_change_test_sheet.withSkippedState(9).taskStatus === 9)", "groupStrategy": true}, {"run": "@kfc/kim:0.0.1", "name": "multi_change_test_kim", "displayName": "KIM 通知", "showInFlow": false, "input": {"title": "'测试完成通知'", "type": "'iteration'", "memberTypes": "['rd']", "message": "'已测试完成，请及时发起MR并进行预发部署'"}, "condition": "context.jobs.multi_change_test_team_confirm_ok.state.noRequired", "groupStrategy": true}, {"name": "multi_change_pre_mr", "run": "@kfc/kdev-mr:0.0.1", "groupStrategy": "XOR", "displayName": "发起集成", "input": {"sourceBranch": "context.variables.branchName", "targetBranch": "context.parent && context.parent.variables.branchName", "relateCrJobName": "'multi_change_cr'"}, "condition": "context.jobs.multi_change_test_team_confirm_ok.state.noRequired"}], "checkpoint": "context.jobs.multi_change_pre_mr.state.isCreated || context.jobs.multi_change_pre_mr.state.isMerged", "checkpointMessages": [{"jobName": "multi_change_pre_mr", "condition": "!context.jobs.multi_change_pre_mr.state.isCreated && !context.jobs.multi_change_pre_mr.state.isMerged", "message": "未发起集成"}]}]}, {"name": "multi_change_iteration", "displayName": "需求开发", "type": "iteration", "forms": {"component": {}, "forms": {"layout": {"type": "void", "x-component": "FormLayout", "x-component-props": {"layout": "vertical"}, "properties": {"branchName": {"type": "string", "title": "迭代分支", "x-component": "BranchSelect", "x-component-props": {"type": "context.app.formValues.Gitlab.featureIterationType", "name": "context.app.formValues.Gitlab.featureIterationName", "gitlabId": "context.app.gitlabId"}, "x-validator": {"gitBranchNameValidator": true}, "x-decorator": "FormItem", "required": true}, "kimGroupId": {"x-component": "KimGroupSelect", "x-decorator": "FormItem", "title": "消息推送群组", "x-hidden": true, "default": "context.app.formValues['消息推送'].kimGroupId"}}}}}, "stages": [{"name": "multi_change_iteration_integrate", "displayName": "集成阶段", "jobs": [{"name": "single_change_dev_checkout_branch", "run": "@kfc/gitlab-checkout:0.0.1", "groupStrategy": "XOR", "displayName": "切换分支", "input": {"sourceBranch": "context.formValues.app.Gitlab.baseBranch", "targetBranch": "context.variables.branchName"}}, {"name": "multi_change_iteration_integrate_for_mr", "run": "@kfc/for:0.0.1", "groupStrategy": "XOR", "displayName": "合入集成分支", "input": {"subFlowJobName": "'multi_change_pre_mr'", "finishedStatus": "({key:'hasMerged',value:true})"}}], "checkpoint": "context.jobs.multi_change_iteration_integrate_for_mr.state.status==='FINISHED'", "checkpointMessages": [{"jobName": "multi_change_iteration_integrate_for_mr", "condition": "!(context.jobs.multi_change_iteration_integrate_for_mr.state.status==='FINISHED')", "message": "请确认MR已经合入"}]}, {"name": "multi_change_iteration_pre", "displayName": "预发阶段", "allowSkip": true, "skipApprovers": ["APP_PRINCIPAL", "APP_ADMIN"], "jobs": [{"name": "lock_master", "displayName": "锁分支", "run": "@kfc/lock:0.0.1", "groupStrategy": "XOR", "input": {"method": "'gitlab'", "params": "{gitlabId: context.application.gitlabId, branchName: context.formValues.app.Gitlab.baseBranch}"}}, {"name": "lock_release", "displayName": "锁分支", "run": "@kfc/lock:0.0.1", "groupStrategy": "XOR", "input": {"method": "'gitlab'", "params": "{gitlabId: context.application.gitlabId, branchName: context.variables.branchName}"}}, {"run": "@kfc/kfx-checklist-prepublish:0.0.5", "name": "checklist_prepublish", "displayName": "部署前检查", "input": {"checklistConfig": "{options: context.formValues.app.检查项.checklistConfigOptions, link: context.variables.checklistConfig.link}", "branchName": "context.variables.branchName", "baseBranch": "context.formValues.app.Gitlab.baseBranch", "openAIAnalysis": "true"}, "allowHide": true, "allowSkip": true, "groupStrategy": true}, {"run": "@halo/pipeline:1.0.2", "name": "pre_pipeline", "displayName": "预发部署", "allowHide": true, "input": {"showHistoryByJob": true, "branchName": "context.variables.branchName", "details": "context.formValues.app.Pipeline.prePipelines", "hookStrategy": "'running-stage'", "nodeInfos": "context.formValues.app.容器云.cloudDeployNode"}, "condition": "context.jobs.lock_master.state.isLocked && context.jobs.lock_release.state.isLocked && context.jobs.checklist_prepublish.withSkippedState(true).isAllDone"}, {"run": "@kfc/cloud-deploy:0.0.1", "name": "pre_cloud_deploy", "displayName": "容器部署", "allowHide": true, "input": {"branchName": "context.variables.branchName", "nodeInfos": "context.formValues.app.容器云.cloudDeployNode", "productType": "'prod'", "productMap": "context.jobs.pre_pipeline.withSkippedState().productMap"}, "condition": "context.jobs.lock_master.state.isLocked && context.jobs.lock_release.state.isLocked"}, {"run": "@kfc/kim-confirm:0.0.4", "name": "pre_rd_confirm", "displayName": "RD 确认预发部署完成", "groupStrategy": true, "allowHide": true, "input": {"kimTitle": "'确认预发部署完成通知'", "kimContent": "`预发流水线已构建完成，请确认是否已经部署完成，也可以进入风驰平台 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/iteration/${context.flow.id})`", "kimButton": "'确认部署完成'", "memberTypes": "['rd']"}, "condition": "context.jobs.lock_master.state.isLocked && context.jobs.lock_release.state.isLocked && context.jobs.pre_pipeline.withSkippedState('FINISHED').status === 'FINISHED'"}, {"run": "@kfc/kim-confirm:0.0.4", "name": "pre_qa_confirm", "displayName": "QA 预发环境验收", "groupStrategy": true, "allowHide": true, "input": {"kimTitle": "'预发环境验收通知'", "kimContent": "`预发部署完成，请进行验收确认，也可以进入风驰平台 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/iteration/${context.flow.id})`", "kimButton": "'确认部署完成'", "memberTypes": "['qa']"}, "condition": "context.jobs.lock_master.state.isLocked && context.jobs.lock_release.state.isLocked && context.jobs.pre_rd_confirm.withSkippedState(true).isConfirm"}, {"run": "@kfc/kim-confirm:0.0.4", "name": "pre_pm_confirm", "displayName": "PM 预发环境验收", "groupStrategy": true, "allowHide": true, "input": {"kimTitle": "'预发环境验收通知'", "kimContent": "`预发部署完成，请进行验收确认，也可以进入风驰平台 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/iteration/${context.flow.id})`", "kimButton": "'确认部署完成'", "memberTypes": "['pm']"}, "condition": "context.jobs.lock_master.state.isLocked && context.jobs.lock_release.state.isLocked && context.jobs.pre_rd_confirm.withSkippedState(true).isConfirm"}, {"run": "@kfc/kim-confirm:0.0.4", "name": "pre_ui_confirm", "displayName": "UI 预发环境验收", "groupStrategy": true, "allowHide": true, "input": {"kimTitle": "'预发环境验收通知'", "kimContent": "`预发部署完成，请进行验收确认，也可以进入风驰平台 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/iteration/${context.flow.id})`", "kimButton": "'确认部署完成'", "memberTypes": "['designer']"}, "condition": "context.jobs.lock_master.state.isLocked && context.jobs.lock_release.state.isLocked && context.jobs.pre_rd_confirm.withSkippedState(true).isConfirm"}, {"name": "multi_change_integrate_team_product_mark", "displayName": "Team 状态翻转 (预发验收完成)", "run": "@kfc/team-status:0.0.3", "groupStrategy": true, "input": {"status": "'PRODUCT_MARK_OK'", "forceRequiredReady": true}, "extra": {"convertInfo": "确认预发验收完成"}, "condition": "context.jobs.pre_qa_confirm.withSkippedState(true).isConfirm && context.jobs.pre_pm_confirm.withSkippedState(true).isConfirm && context.jobs.pre_ui_confirm.withSkippedState(true).isConfirm"}, {"run": "@kfc/kim:0.0.1", "name": "multi_change_iteration_pre_kim", "displayName": "KIM 通知", "showInFlow": false, "groupStrategy": true, "input": {"title": "'预发验收完成通知'", "type": "'iteration'", "memberTypes": "['rd']", "message": "'预发验收已完成，请进行线上部署'"}, "condition": "context.jobs.pre_qa_confirm.withSkippedState(true).isConfirm && context.jobs.pre_pm_confirm.withSkippedState(true).isConfirm && context.jobs.pre_ui_confirm.withSkippedState(true).isConfirm"}, {"run": "@kfc/kfc-branch-backward-check:0.0.2", "groupStrategy": "XOR", "displayName": "分支落后检测", "name": "pre_backward", "showInFlow": false, "input": {"targetBranch": "context.variables.branchName", "baseBranch": "context.formValues.app.Gitlab.baseBranch"}}], "checkpoint": "context.jobs.pre_qa_confirm.withSkippedState(true).isConfirm && context.jobs.pre_pm_confirm.withSkippedState(true).isConfirm && context.jobs.pre_ui_confirm.withSkippedState(true).isConfirm && !context.jobs.pre_backward.withSkippedState(false).isBackward", "checkpointMessages": [{"jobName": "pre_qa_confirm", "condition": "!context.jobs.pre_qa_confirm.withSkippedState(true).isConfirm", "message": "请 QA 先进行预发验收"}, {"jobName": "pre_pm_confirm", "condition": "!context.jobs.pre_pm_confirm.withSkippedState(true).isConfirm", "message": "请 PM 先进行预发验收"}, {"jobName": "pre_ui_confirm", "condition": "!context.jobs.pre_ui_confirm.withSkippedState(true).isConfirm", "message": "请 UI 先进行预发验收"}, {"jobName": "pre_backward", "condition": "context.jobs.pre_backward.withSkippedState(false).isBackward", "message": "当前分支落后于基线分支，请更新后再上线"}]}, {"name": "multi_change_iteration_prod", "displayName": "发布阶段", "jobs": [{"run": "@halo/pipeline:1.0.2", "name": "multi_change_iteration_prod_pipeline", "allowHide": true, "displayName": "线上产物构建", "input": {"showHistoryByJob": true, "branchName": "context.variables.branchName", "details": "context.formValues.app.Pipeline.onlinePipelines", "hookStrategy": "'running-stage'", "nodeInfos": "context.formValues.app.容器云.cloudDeployNode"}, "condition": "(await context.isLocked('lock_master')) && (await context.isLocked('lock_release'))"}, {"run": "@kfc/kim-group:0.0.2", "name": "multi_change_iteration_prod_kim_group", "displayName": "KIM 群通知", "showInFlow": false, "input": {"memberTypes": "['pm', 'qa', 'rd']", "kimTitle": "'线上产物构建完成通知'", "kimContent": "`线上产物构建已完成，@participants 可以进入风驰平台进行线上部署或验收 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/change/${context.flow.id})`", "kimGroupId": "context.variables.kimGroupId"}, "condition": "context.jobs.multi_change_iteration_prod_pipeline.withSkippedState('FINISHED').status === 'FINISHED'", "groupStrategy": true}, {"run": "@kfc/cloud-deploy:0.0.1", "name": "multi_change_iteration_prod_cloud_deploy", "displayName": "容器部署", "allowHide": true, "input": {"branchName": "context.variables.branchName", "nodeInfos": "context.formValues.app.容器云.cloudDeployNode", "productType": "'prod'", "productMap": "context.jobs.multi_change_iteration_prod_pipeline.withSkippedState().productMap"}, "condition": "(await context.isLocked('lock_master')) && (await context.isLocked('lock_release')) && context.jobs.multi_change_iteration_prod_pipeline.withSkippedState('FINISHED').status === 'FINISHED'"}, {"name": "multi_change_iteration_prod_kfx", "run": "@kfc/kfc-kcfe-deploy-kfx:3.0.0", "displayName": "KFX 部署", "allowHide": true, "input": {"deployMode": "'order'", "kfxAppId": "context.formValues.app.KFX.kfxAppId", "env": "'prod'", "branchName": "context.variables.branchName", "productMessages": "Object.values(context.jobs.multi_change_iteration_prod_pipeline.withSkippedState().productMap || {}).flat()", "radarId": "context.formValues.app.Radar.RadarProject"}, "condition": "(await context.isLocked('lock_master')) && (await context.isLocked('lock_release')) && context.jobs.multi_change_iteration_prod_pipeline.withSkippedState('FINISHED').status === 'FINISHED'"}, {"run": "@kfc/kfc-publish-monitor:1.0.0", "name": "publish_monitor", "displayName": "Radar发布监控", "input": {"projectIds": "context.formValues.app.Radar.RadarProject", "panels": "context.formValues.app.Radar.RadarPanel", "publishInfo": "[context.jobs.multi_change_iteration_prod_kfx.withSkippedState().publishInfo,context.jobs.multi_change_iteration_prod_cloud_deploy.withSkippedState().publishInfo,context.jobs.multi_change_iteration_prod_pipeline.withSkippedState().publishInfo]", "viewType": "context.formValues.app.Radar.BoardViewType"}, "allowHide": true}, {"run": "@kfc/kim-confirm:0.0.4", "name": "prod_rd_confirm", "displayName": "RD 确认上线完成", "input": {"kimTitle": "'确认上线完成通知'", "kimContent": "`发布流水线已构建完成，请确认是否已经部署完成，也可以进入风驰平台 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/iteration/${context.flow.id})`", "kimButton": "'确认部署完成'", "memberTypes": "['rd']"}, "condition": "(await context.isLocked('lock_master')) && (await context.isLocked('lock_release')) && context.jobs.multi_change_iteration_prod_pipeline.withSkippedState('FINISHED').status === 'FINISHED'", "groupStrategy": true}, {"run": "@kfc/kim-confirm:0.0.4", "name": "prod_qa_confirm", "displayName": "QA 线上环境验收", "input": {"kimTitle": "'线上环境验收通知'", "kimContent": "`线上部署完成，请进行验收确认，也可以进入风驰平台 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/iteration/${context.flow.id})`", "kimButton": "'确认验收通过'", "memberTypes": "['qa']"}, "condition": "context.jobs.prod_rd_confirm.state.isConfirm", "groupStrategy": true}, {"run": "@kfc/kim-confirm:0.0.4", "name": "prod_pm_confirm", "displayName": "PM 线上环境验收", "input": {"kimTitle": "'线上环境验收通知'", "kimContent": "`线上部署完成，请进行验收确认，也可以进入风驰平台 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/iteration/${context.flow.id})`", "kimButton": "'确认验收通过'", "memberTypes": "['pm']"}, "condition": "context.jobs.prod_rd_confirm.state.isConfirm", "groupStrategy": true}, {"run": "@kfc/kim-confirm:0.0.4", "name": "prod_ui_confirm", "displayName": "UI 线上环境验收", "input": {"kimTitle": "'线上环境验收通知'", "kimContent": "`线上部署完成，请进行验收确认，也可以进入风驰平台 [查看详情](https://kfc.corp.kuaishou.com/applications/${context.application.id}/iteration/${context.flow.id})`", "kimButton": "'确认验收通过'", "memberTypes": "['designer']"}, "condition": "context.jobs.prod_rd_confirm.state.isConfirm", "groupStrategy": true}, {"name": "prod_merge_base", "run": "@kfc/gitlab-merge-to-base:0.0.3", "groupStrategy": "XOR", "displayName": "回写基线", "input": {"baseBranch": "context.formValues.app.Gitlab.baseBranch", "sourceBranch": "context.variables.branchName", "autoMerge": "context.formValues.app.Gitlab.autoMerge", "isDeleteSourceBranch": "context.formValues.app.Gitlab.isDeleteSourceBranch"}, "defaultCurrentUser": "context.jobs.prod_rd_confirm.currentUser.isConfirm", "condition": "(await context.isLocked('lock_master')) && (await context.isLocked('lock_release')) && context.jobs.prod_qa_confirm.state.isConfirm && context.jobs.prod_pm_confirm.state.isConfirm && context.jobs.prod_ui_confirm.state.isConfirm"}, {"run": "@kfc/kim:0.0.1", "name": "prod_kim", "displayName": "KIM 通知", "showInFlow": false, "input": {"title": "'线上验收完成通知'", "type": "'iteration'", "memberTypes": "['rd']", "message": "'线上验收通过，需求已完成'"}, "condition": "context.jobs.prod_merge_base.state.status === 'FINISHED'", "groupStrategy": true}, {"name": "multi_change_integreate_team_published", "displayName": "Team 状态翻转 (上线完成)", "run": "@kfc/team-status:0.0.3", "input": {"status": "'PUBLISH_FINISH'", "forceRequiredReady": true}, "extra": {"convertInfo": "确认上线完成"}, "condition": "context.jobs.prod_merge_base.state.status === 'FINISHED'", "groupStrategy": true}, {"run": "@kfc/kim:0.0.1", "name": "multi_change_iteration_prod_kim", "displayName": "KIM 通知", "showInFlow": false, "input": {"title": "'上线完成通知'", "type": "'iteration'", "memberTypes": "['pm', 'qa', 'rd', 'designer']", "message": "'上线已完成'"}, "condition": "context.jobs.prod_merge_base.state.status === 'FINISHED'", "groupStrategy": true}, {"name": "unlock_master", "displayName": "解锁分支", "run": "@kfc/unlock:0.0.1", "groupStrategy": "XOR", "input": {"ref": "'lock_master'"}, "condition": "context.jobs.prod_merge_base.state.status === 'FINISHED'"}, {"name": "unlock_release", "displayName": "解锁分支", "run": "@kfc/unlock:0.0.1", "groupStrategy": "XOR", "input": {"ref": "'lock_release'"}, "condition": "context.jobs.unlock_master.state.isUnlocked"}], "checkpoint": "context.jobs.unlock_release.state.isUnlocked"}]}]}