#!/bin/bash

set -x

# 默认不启用签名和公证
ENABLE_CODESIGN=${ENABLE_CODESIGN:-"true"}
ENABLE_NOTARIZE=${ENABLE_NOTARIZE:-"true"}

# 从环境变量读取quality，默认为"dev"
QUALITY=${QUALITY:-"dev"}

# 从第一个和第二个参数读取签名和公证选项（如果它们不是以--开头的选项）
if [ ! -z "$1" ] && [[ ! "$1" == --* ]]; then
  ENABLE_CODESIGN="$1"
  # 移除第一个参数，以便后续解析
  shift
fi

if [ ! -z "$1" ] && [[ ! "$1" == --* ]]; then
  ENABLE_NOTARIZE="$1"
  # 移除第一个参数，以便后续解析
  shift
fi

CODESIGN_IDENTITY=${CODESIGN_IDENTITY:-""}
APPLE_ID=${APPLE_ID:-""}
APPLE_APP_SPECIFIC_PASSWORD=${APPLE_APP_SPECIFIC_PASSWORD:-""}
APPLE_TEAM_ID=${APPLE_TEAM_ID:-""}

# 使用BUILDTYPE变量
echo "Build type is: $BUILDTYPE"

echo "BUILD_MIN is: $BUILD_MIN"

if [ $PLUGIN_BRANCH != "" ]; then
    # 进入子模块目录
    cd src/vs/kwaipilot

    # 切换到指定分支
    git fetch origin ${PLUGIN_BRANCH}
    git checkout ${PLUGIN_BRANCH}
    git pull origin ${PLUGIN_BRANCH}

    echo "切换子模块 kwaipilot 到指定分支: ${PLUGIN_BRANCH}"

    # 返回项目根目录
    cd ../../..
fi

echo "检查目录权限"
whoami
if [ -d "/Users/<USER>/Library/Caches/electron" ]; then
  ls -ld /Users/<USER>/Library/Caches/electron
else
  echo "目录 /Users/<USER>/Library/Caches/electron 不存在"
fi

# 使用BUILDTYPE变量
if [ -z "$BUILDTYPE" ]; then
  # 如果没有指定BUILDTYPE，则根据当前平台和架构自动设置
  PLATFORM=$(uname | tr '[:upper:]' '[:lower:]')
  ARCH=$(uname -m)

  # 映射架构
  if [ "$ARCH" == "x86_64" ]; then
    ARCH="x64"
  elif [ "$ARCH" == "arm64" ] || [ "$ARCH" == "aarch64" ]; then
    ARCH="arm64"
  elif [ "$ARCH" == "armv7l" ]; then
    ARCH="armhf"
  fi

  # 映射平台
  if [ "$PLATFORM" == "darwin" ]; then
    PLATFORM="darwin"
  elif [ "$PLATFORM" == "linux" ]; then
    PLATFORM="linux"
  elif [[ "$PLATFORM" == *"mingw"* ]] || [[ "$PLATFORM" == *"msys"* ]] || [[ "$PLATFORM" == *"cygwin"* ]]; then
    PLATFORM="win32"
  fi

  # 自动设置BUILDTYPE
  BUILDTYPE="vscode-$PLATFORM-$ARCH"
  echo "未指定BUILDTYPE，自动设置为: $BUILDTYPE"
else
  echo "使用指定的BUILDTYPE: $BUILDTYPE"

  # 从BUILDTYPE解析出PLATFORM和ARCH
  if [[ "$BUILDTYPE" =~ vscode-([^-]+)-([^-]+) ]]; then
    export PLATFORM=${BASH_REMATCH[1]}
    export ARCH=${BASH_REMATCH[2]}
    echo "解析出平台: $PLATFORM, 架构: $ARCH"
  else
    echo "警告: BUILDTYPE格式不正确，可能无法被build-skip-typecheck.sh正确识别"
  fi
fi

# 确保PLATFORM和ARCH被导出，以便被子脚本使用
export PLATFORM
export ARCH
export QUALITY

echo "开始打包"
echo "平台: $PLATFORM"
echo "架构: $ARCH"
echo "质量版本: $QUALITY"
echo "构建类型: $BUILDTYPE"
node -v
npm -v

update_product_json_quality() {
  local quality_value="$1"
  local product_json="product.json"

  echo "使用python更新 $product_json 中的 quality 字段为: $quality_value"

  python3 -c "
import sys, json
path = '$product_json'
with open(path, 'r', encoding='utf-8') as f:
    data = json.load(f)
data['quality'] = '$quality_value'
with open(path, 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
" || { echo '使用python更新product.json失败'; exit 1; }
}

# 更新product.json中的quality
update_product_json_quality "$QUALITY"

# 设置npm镜像和electron镜像
echo "配置npm和electron镜像..."
export NPM_CONFIG_REGISTRY=https://npm.corp.kuaishou.com
export ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/

export GITHUB_TOKEN=****************************************

# 下载并安装 ide-agent
echo "开始下载并安装 ide-agent..."
AGENT_URL=${AGENT_URL:-"https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-binary/kwaipilot-binary-1.0.6.tar.gz"}
echo "ide-agent 下载地址: $AGENT_URL"
AGENT_DIR="./extensions/kwaipilot/local-agent"
TEMP_DIR="/tmp/kwaipilot-binary"

# 创建目标目录
mkdir -p "$AGENT_DIR"

# 下载并解压 ide-agent
curl -L "$AGENT_URL" | tar xz -C /tmp
echo "ide-agent 下载完成，开始复制文件..."

# 根据当前平台和架构选择对应的版本目录
BINARY_PLATFORM_DIR="$TEMP_DIR/${PLATFORM}-${ARCH}"

if [ ! -d "$BINARY_PLATFORM_DIR" ]; then
    echo "错误: 找不到对应平台 ${PLATFORM}-${ARCH} 的二进制文件"
    exit 1
fi

# 复制整个平台特定目录
cp -R "$BINARY_PLATFORM_DIR"/* "$AGENT_DIR/"

echo "ide-agent 安装完成"

# 清理临时文件
rm -rf "$TEMP_DIR"

# 安装依赖
echo "安装项目依赖..."
export electron_config_cache="./electron-cache"
export CI="KCI_PIPELINE"
export ELECTRON_SKIP_BINARY_DOWNLOAD="1"
npm install

# 构建React视图
echo "构建React视图..."
./build-react.sh

# 执行gulp构建
if [ "$BUILD_MIN" = "true" ]; then
  PRESERVE_SOURCEMAPS=$PRESERVE_SOURCEMAPS npm run gulp "$BUILDTYPE-min"
else
  PRESERVE_SOURCEMAPS=$PRESERVE_SOURCEMAPS npm run gulp "$BUILDTYPE"
fi

echo "打包完成"

# 如果是macOS平台且启用了签名，则调用sign.sh进行签名和公证
if [ "$PLATFORM" == "darwin" ] && [ "$ENABLE_CODESIGN" == "true" ]; then
  echo "开始调用sign.sh进行签名和公证..."

  # 查找应用路径 - 修改为更灵活的查找方式
  APP_BASE_PATH="."

  # 首先尝试查找项目上一层目录下的 VSCode 目录
  VSCode_DIR=$(find ../ -maxdepth 1 -type d -name "VSCode*" | head -n 1)

  if [ -n "$VSCode_DIR" ]; then
    APP_PATH="$VSCode_DIR"
    echo "找到 VSCode 目录: $APP_PATH"
  else
    # 如果没找到 VSCode 目录，回退到原来的 .build/electron 目录查找
    APP_BASE_PATH=".build/electron"
    APP_PATH="$APP_BASE_PATH/"

    # 如果路径不存在，尝试查找匹配的目录
    if [ ! -d "$APP_PATH" ]; then
      echo "警告: 找不到指定的应用目录: $APP_PATH"
      echo "尝试查找匹配的目录..."

      # 查找可能的目录
      POSSIBLE_PATHS=$(find "$APP_BASE_PATH" -type d -name "*$PLATFORM*$ARCH*" 2>/dev/null)

      if [ ! -z "$POSSIBLE_PATHS" ]; then
        # 使用第一个匹配的目录
        APP_PATH=$(echo "$POSSIBLE_PATHS" | head -n 1)
        echo "找到可能的应用目录: $APP_PATH"
      else
        echo "错误: 无法找到任何匹配的应用目录"
        exit 1
      fi
    fi
  fi

  # 查找应用名称
  APP_NAME=$(ls "$APP_PATH" | grep ".app$" | head -n 1)
  if [ -z "$APP_NAME" ]; then
    echo "错误: 在 $APP_PATH 中找不到 .app 文件"
    exit 1
  fi

  FULL_APP_PATH="$APP_PATH/$APP_NAME"
  echo "应用路径: $FULL_APP_PATH"

  # 调用sign.sh进行签名和公证
  ./sign.sh "$FULL_APP_PATH" "$ENABLE_CODESIGN" "$ENABLE_NOTARIZE" "$CODESIGN_IDENTITY" "$APPLE_ID" "$APPLE_APP_SPECIFIC_PASSWORD" "$APPLE_TEAM_ID" $KCONF_TOKEN
fi

echo "所有任务完成"
